############## Unused code


import ctypes
import time

class SystemIdleChecker:
    def __init__(self):
        self.struct_lastinputinfo = ctypes.Structure
        class LASTINPUTINFO(ctypes.Structure):
            _fields_ = [("cbSize", ctypes.c_uint), ("dwTime", ctypes.c_uint)]
        self.LASTINPUTINFO = LASTINPUTINFO

    def get_idle_duration(self):
        last_input_info = self.LASTINPUTINFO()
        last_input_info.cbSize = ctypes.sizeof(last_input_info)
        ctypes.windll.user32.GetLastInputInfo(ctypes.byref(last_input_info))
        millis = ctypes.windll.kernel32.GetTickCount() - last_input_info.dwTime
        return millis / 1000.0  # in seconds

    def is_user_active(self, threshold_sec=10):
        idle_time = self.get_idle_duration()
        return idle_time < threshold_sec

if __name__ == "__main__":
    idle_checker = SystemIdleChecker()
    while True:
        if idle_checker.is_user_active(10):
            print("User is ACTIVE (any input detected)")
        else:
            print("User is INACTIVE")
        time.sleep(5)
