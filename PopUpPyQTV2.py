import sys, os
import json
import traceback
import logging
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFrame,
    QLabel, QComboBox, QLineEdit, QListWidget, QPushButton, QStackedWidget,
    QSizePolicy, QMessageBox, QListWidgetItem, QScrollArea, QMenu, QDialog, QSpinBox
)
from PyQt5.QtCore import Qt, QTimer, QEvent, QCoreApplication
import win32gui
import win32api, win32con
from monHelper import CLogger

# MeetingInput class remains unchanged as per the requirement
class MeetingInput(QFrame):
    def __init__(self, user, dict_UserData, dictCustomerData, meeting_templates, allowed_customers, favorite_users, parent=None):
        super().__init__(parent)
        try:
            self.user = user
            self.dict_UserData = dict_UserData
            self.dictCustomerData = dictCustomerData
            self.meeting_templates = meeting_templates
            self.allowed_customers = allowed_customers
            self.favorite_users = favorite_users  # Now a set of full names

            self.setObjectName("MeetingInput")
            layout = QGridLayout(self)
            layout.setSpacing(10)
            layout.setContentsMargins(10, 10, 10, 10)

            layout.addWidget(QLabel("<b>With</b><font color='#FF6B6B'>*</font><b>:</b>"), 0, 0)
            with_layout = QVBoxLayout()
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Search users...")
            self.search_input.setStyleSheet("background-color: #dde2e8;")
            with_layout.addWidget(self.search_input)
            self.with_list = QListWidget()
            self.with_list.setStyleSheet("background-color: #dde2e8;")
            self.with_list.setContextMenuPolicy(Qt.CustomContextMenu)
            self.with_list.customContextMenuRequested.connect(self.show_user_context_menu)
            self.populate_with_list()
            with_layout.addWidget(self.with_list)
            layout.addLayout(with_layout, 0, 1, 1, 3)

            layout.addWidget(QLabel("<b>Selected Users:</b>"), 1, 0)
            self.selected_users_value = QLabel("None")
            self.selected_users_value.setStyleSheet("color: #343A40; font-size: 16px;")
            self.selected_users_value.setWordWrap(True)
            layout.addWidget(self.selected_users_value, 1, 1)

            layout.addWidget(QLabel("<b>Customer</b><font color='#FF6B6B'>*</font><b>:</b>"), 2, 0)
            self.customer_combo = QComboBox()
            self.customer_combo.addItem("Select Customer")
            customer_list = sorted(self.get_customer_list())
            self.customer_combo.addItems(customer_list)
            self.customer_combo.setStyleSheet("QComboBox { background-color: #dde2e8; }")
            self.customer_combo.currentIndexChanged.connect(self.on_customer_changed)
            layout.addWidget(self.customer_combo, 2, 1)

            layout.addWidget(QLabel("<b>Project</b><font color='#FF6B6B'>*</font><b>:</b>"), 3, 0)
            self.project_combo = QComboBox()
            self.project_combo.setStyleSheet("QComboBox { background-color: #dde2e8; }")
            layout.addWidget(self.project_combo, 3, 1)

            layout.addWidget(QLabel("<b>Topic</b>:"), 4, 0)
            self.topic_input = QLineEdit()
            self.topic_input.setPlaceholderText("Enter topic")
            self.topic_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.topic_input, 4, 1)

            layout.addWidget(QLabel("<b>Meeting Template</b><b>:</b>"), 5, 0)
            self.meeting_template_list = QListWidget()
            self.meeting_template_list.setStyleSheet("background-color: #dde2e8;")
            self.meeting_template_list.setWordWrap(True)
            self.meeting_template_list.setContextMenuPolicy(Qt.CustomContextMenu)
            self.meeting_template_list.customContextMenuRequested.connect(self.show_template_context_menu)
            self.update_templates(self.meeting_templates)
            self.meeting_template_list.setSelectionMode(QListWidget.SingleSelection)
            self.meeting_template_list.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            self.meeting_template_list.currentItemChanged.connect(self.on_template_selected)
            self.meeting_template_list.itemClicked.connect(self.on_template_clicked)
            layout.addWidget(self.meeting_template_list, 5, 1)
            item_height = self.meeting_template_list.sizeHintForRow(0)
            self.meeting_template_list.setMaximumHeight(item_height * 5 + 2)       
            self.meeting_template_list.clearSelection()

            layout.addWidget(QLabel("<b>Template Name</b><b>:</b>"), 6, 0)
            self.template_name_input = QLineEdit()
            self.template_name_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.template_name_input, 6, 1)
            self.save_template_btn = QPushButton("Save Template")
            self.save_template_btn.clicked.connect(lambda: self.window().save_template_from_input(self, "meeting"))
            layout.addWidget(self.save_template_btn, 6, 2)
            self.delete_template_btn = QPushButton("Delete Template")
            self.delete_template_btn.clicked.connect(lambda: self.window().delete_template(self, "meeting"))
            layout.addWidget(self.delete_template_btn, 6, 3)

            self.with_list.itemSelectionChanged.connect(self.update_selected_users_label)
            self.search_input.textChanged.connect(self.filter_users)
            self.on_customer_changed()
            self.with_list.installEventFilter(self)
            self.meeting_template_list.installEventFilter(self)
        except Exception:
            traceback.print_exc()

    def on_template_clicked(self, item):
        try:
            self.meeting_template_list.setCurrentItem(item)
            self.on_template_selected(item, None)
        except Exception:
            traceback.print_exc()        

    def eventFilter(self, obj, event):
        if obj == self.with_list and event.type() == QEvent.KeyPress:
            key = event.key()
            if Qt.Key_A <= key <= Qt.Key_Z:
                self.search_input.setFocus()
                self.search_input.setText(event.text())
                self.search_input.selectAll()
                return True
        elif obj == self.meeting_template_list and event.type() == QEvent.KeyPress:
            key = event.key()
            if Qt.Key_A <= key <= Qt.Key_Z:
                letter = event.text().lower()
                for i in range(self.meeting_template_list.count()):
                    item = self.meeting_template_list.item(i)
                    name = item.text()[2:].lower()
                    if name.startswith(letter):
                        self.meeting_template_list.setCurrentRow(i)
                        self.meeting_template_list.scrollToItem(item)
                        break
                return True
        return super().eventFilter(obj, event)

    def get_customer_list(self):
        try:
            user_data = self.dict_UserData.get(self.user)
            return user_data.get("List_Of_Customer", []) if user_data else []
        except Exception:
            traceback.print_exc()
            return []

    def populate_with_list(self):
        try:
            self.with_list.clear()
            seen_names = set()
            user_items = []
            for key, user_data in self.dict_UserData.items():
                full_name = user_data.get("fullName", key)
                if full_name not in seen_names:
                    seen_names.add(full_name)
                    user_items.append((key, full_name))
            normal_users = [(key, name) for key, name in user_items if not name.startswith("--")]
            special_users = [(key, name) for key, name in user_items if name.startswith("--")]
            normal_users.sort(key=lambda x: (-(x[1] in self.favorite_users), x[1].lower()))
            special_users.sort(key=lambda x: (-(x[1] in self.favorite_users), x[1].lower()))
            sorted_users = normal_users + special_users
            for key, full_name in sorted_users:
                prefix = "★ " if full_name in self.favorite_users else "☆ "
                item = QListWidgetItem(prefix + full_name)
                item.setData(Qt.UserRole, key)
                self.with_list.addItem(item)
            self.with_list.setSelectionMode(QListWidget.MultiSelection)
        except Exception:
            traceback.print_exc()

    def filter_users(self, text):
        try:
            text = text.lower()
            for i in range(self.with_list.count()):
                item = self.with_list.item(i)
                full_name = item.text()[2:].lower()
                names = full_name.split()
                first_name = names[0] if names else ""
                last_name = names[-1] if len(names) > 1 else ""
                item.setHidden(not (first_name.startswith(text) or last_name.startswith(text)))
        except Exception:
            traceback.print_exc()

    def select_first_visible_user(self):
        try:
            for i in range(self.with_list.count()):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(True)
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_next_visible_user(self):
        try:
            current_row = self.with_list.currentRow()
            for i in range(current_row + 1, self.with_list.count()):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(not item.isSelected())
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_previous_visible_user(self):
        try:
            current_row = self.with_list.currentRow()
            for i in range(current_row - 1, -1, -1):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(not item.isSelected())
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_user_selection(self, item):
        try:
            item.setSelected(not item.isSelected())
            self.update_selected_users_label()
        except Exception:
            traceback.print_exc()

    def keyPressEvent(self, event):
        try:
            if self.with_list.hasFocus() and event.text().isalpha():
                letter = event.text().lower()
                for i in range(self.with_list.count()):
                    item = self.with_list.item(i)
                    name = item.text()[2:].strip()
                    if name.lower().startswith(letter):
                        self.with_list.setCurrentRow(i)
                        self.with_list.scrollToItem(item)
                        break
                event.accept()
                return
            if event.key() in (Qt.Key_Return, Qt.Key_Enter):
                if self.search_input.hasFocus() and self.search_input.text().strip():
                    self.select_first_visible_user()
                    self.with_list.setFocus()
                elif self.with_list.hasFocus():
                    current_item = self.with_list.currentItem()
                    if current_item and not current_item.isHidden():
                        self.toggle_user_selection(current_item)
                elif self.save_template_btn.hasFocus():
                    self.save_template_btn.click()
            elif event.key() == Qt.Key_Down and self.with_list.hasFocus():
                self.toggle_next_visible_user()
            elif event.key() == Qt.Key_Up and self.with_list.hasFocus():
                self.toggle_previous_visible_user()
            super().keyPressEvent(event)
        except Exception:
            traceback.print_exc()

    def update_selected_users_label(self):
        try:
            selected_keys = [item.data(Qt.UserRole) for item in self.with_list.selectedItems()]
            selected_names = [self.dict_UserData[key].get("fullName", key) for key in selected_keys]
            self.selected_users_value.setText(", ".join(selected_names) if selected_names else "None")
        except Exception:
            traceback.print_exc()

    def on_customer_changed(self):
        try:
            selected_customer = self.customer_combo.currentText()
            self.project_combo.clear()
            project_list = sorted(self.dictCustomerData.get(selected_customer, []))
            self.project_combo.addItems(project_list if project_list else ["Select Project"])
        except Exception:
            traceback.print_exc()

    def on_template_selected(self, current, previous):
        try:
            if current:
                template_name = current.data(Qt.UserRole)
                data = self.meeting_templates.get(template_name, {})
                customer = data.get("customer", "")
                customer_index = self.customer_combo.findText(customer)
                if customer_index >= 0:
                    self.customer_combo.setCurrentIndex(customer_index)
                project = data.get("project", "")
                project_index = self.project_combo.findText(project)
                if project_index >= 0:
                    self.project_combo.setCurrentIndex(project_index)
                with_users = data.get("with", [])
                self.with_list.clearSelection()
                for full_name in with_users:
                    for i in range(self.with_list.count()):
                        item = self.with_list.item(i)
                        if item.text()[2:].strip() == full_name:
                            item.setSelected(True)
                            break
                self.update_selected_users_label()
        except Exception:
            traceback.print_exc()

    def update_templates(self, templates):
        try:
            self.meeting_templates = templates
            current_item = self.meeting_template_list.currentItem()
            current_name = current_item.data(Qt.UserRole) if current_item else None
            self.meeting_template_list.clear()
            sorted_templates = sorted(templates.items(), key=lambda x: (-x[1].get("favorite", False), x[0].lower()))
            for name, data in sorted_templates:
                prefix = "★ " if data.get("favorite", False) else "☆ "
                item = QListWidgetItem(prefix + name)
                item.setData(Qt.UserRole, name)
                self.meeting_template_list.addItem(item)
            if current_name:
                for i in range(self.meeting_template_list.count()):
                    if self.meeting_template_list.item(i).data(Qt.UserRole) == current_name:
                        self.meeting_template_list.setCurrentRow(i)
                        break
            else:
                self.meeting_template_list.setCurrentRow(0 if self.meeting_template_list.count() > 0 else -1)
            if self.meeting_template_list.count() > 0:
                item_height = self.meeting_template_list.sizeHintForRow(0)
                self.meeting_template_list.setMaximumHeight(item_height * min(5, self.meeting_template_list.count()) + 2)
            else:
                self.meeting_template_list.setMaximumHeight(100)
        except Exception:
            traceback.print_exc()

    def clear(self):
        try:
            self.with_list.clearSelection()
            self.customer_combo.setCurrentIndex(0)
            self.project_combo.clear()
            self.project_combo.addItem("Select Project")
            self.meeting_template_list.clearSelection()
            self.template_name_input.clear()
            self.search_input.clear()
            self.reset_highlights()
            self.selected_users_value.setText("None")
        except Exception:
            traceback.print_exc()

    def highlight_field(self, widget):
        try:
            widget.setStyleSheet("border: 2px solid #FF6B6B; background-color: #F8F9FA; color: #343A40;")
        except Exception:
            traceback.print_exc()

    def reset_highlight(self, widget):
        try:
            widget.setStyleSheet("border: 1px solid #CED4DA; background-color: #F8F9FA; color: #343A40;")
        except Exception:
            traceback.print_exc()

    def reset_highlights(self):
        try:
            self.reset_highlight(self.with_list)
            self.reset_highlight(self.customer_combo)
            self.reset_highlight(self.project_combo)
        except Exception:
            traceback.print_exc()

    def validate(self):
        try:
            is_valid = True
            if not self.with_list.selectedItems():
                self.highlight_field(self.with_list)
                is_valid = False
            else:
                self.reset_highlight(self.with_list)
            if self.customer_combo.currentText() == "Select Customer":
                self.highlight_field(self.customer_combo)
                is_valid = False
            else:
                self.reset_highlight(self.customer_combo)
            if self.project_combo.currentText() == "Select Project":
                self.highlight_field(self.project_combo)
                is_valid = False
            else:
                self.reset_highlight(self.project_combo)
            return is_valid
        except Exception:
            traceback.print_exc()
            return False

    def get_selected_user_keys(self):
        try:
            return [item.data(Qt.UserRole) for item in self.with_list.selectedItems()]
        except Exception:
            traceback.print_exc()
            return []

    def show_user_context_menu(self, position):
        try:
            item = self.with_list.itemAt(position)
            if item:
                key = item.data(Qt.UserRole)
                full_name = self.dict_UserData[key].get("fullName", key)
                is_favorite = full_name in self.favorite_users
                menu = QMenu()
                action_text = "Remove from Favorites" if is_favorite else "Add to Favorites"
                action = menu.addAction(action_text)
                action.triggered.connect(lambda: self.toggle_user_favorite(key))
                menu.exec_(self.with_list.mapToGlobal(position))
        except Exception:
            traceback.print_exc()

    def toggle_user_favorite(self, key):
        try:
            full_name = self.dict_UserData[key].get("fullName", key)
            if full_name in self.favorite_users:
                self.favorite_users.remove(full_name)
            else:
                self.favorite_users.add(full_name)
            os.makedirs("favorites", exist_ok=True)
            with open(f"favorites/{self.user}_favorites.json", "w") as f:
                json.dump(list(self.favorite_users), f)
            self.window().update_all_with_lists()
        except Exception:
            traceback.print_exc()

    def show_template_context_menu(self, position):
        try:
            item = self.meeting_template_list.itemAt(position)
            if item:
                name = item.data(Qt.UserRole)
                is_favorite = self.meeting_templates[name].get("favorite", False)
                menu = QMenu()
                action_text = "Remove from Favorites" if is_favorite else "Add to Favorites"
                action = menu.addAction(action_text)
                action.triggered.connect(lambda: self.toggle_template_favorite(name))
                menu.exec_(self.meeting_template_list.mapToGlobal(position))
        except Exception:
            traceback.print_exc()

    def toggle_template_favorite(self, name):
        try:
            if name in self.meeting_templates:
                self.meeting_templates[name]["favorite"] = not self.meeting_templates[name].get("favorite", False)
                os.makedirs("templates", exist_ok=True)
                file_path = f"templates/{self.user}_template.json"
                with open(file_path, "w") as f:
                    json.dump(self.meeting_templates, f, indent=4)
                self.window().update_all_meeting_inputs()
        except Exception:
            traceback.print_exc()

class DndInput(QFrame):
    def __init__(self, user, dict_UserData, dictCustomerData, dnd_templates, allowed_customers, favorite_users, parent=None):
        super().__init__(parent)
        try:
            self.user = user
            self.dict_UserData = dict_UserData
            self.dictCustomerData = dictCustomerData
            self.dnd_templates = dnd_templates
            self.allowed_customers = allowed_customers
            self.favorite_users = favorite_users  # Added favorite_users parameter

            self.setObjectName("DndInput")
            layout = QGridLayout(self)
            layout.setSpacing(10)
            layout.setContentsMargins(10, 10, 10, 10)

            layout.addWidget(QLabel("<b>With</b><b>:</b>"), 0, 0)
            with_layout = QVBoxLayout()
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Search users...")
            self.search_input.setStyleSheet("background-color: #dde2e8;")
            with_layout.addWidget(self.search_input)
            self.with_list = QListWidget()
            self.with_list.setStyleSheet("background-color: #dde2e8;")
            self.with_list.setContextMenuPolicy(Qt.CustomContextMenu)
            self.with_list.customContextMenuRequested.connect(self.show_user_context_menu)
            self.populate_with_list()
            with_layout.addWidget(self.with_list)
            layout.addLayout(with_layout, 0, 1, 1, 3)

            layout.addWidget(QLabel("<b>Selected Users:</b>"), 1, 0)
            self.selected_users_value = QLabel("None")
            self.selected_users_value.setStyleSheet("color: #343A40; font-size: 16px;")
            layout.addWidget(self.selected_users_value, 1, 1)

            layout.addWidget(QLabel("<b>Customer</b><font color='#FF6B6B'>*</font><b>:</b>"), 2, 0)
            self.customer_combo = QComboBox()
            self.customer_combo.addItem("Select Customer")
            self.customer_combo.addItems(sorted(self.get_customer_list()))
            self.customer_combo.setStyleSheet("QComboBox { background-color: #dde2e8; }")
            self.customer_combo.currentIndexChanged.connect(self.on_customer_changed)
            layout.addWidget(self.customer_combo, 2, 1)

            layout.addWidget(QLabel("<b>Project</b><font color='#FF6B6B'>*</font><b>:</b>"), 3, 0)
            self.project_combo = QComboBox()
            self.project_combo.setStyleSheet("QComboBox { background-color: #dde2e8; }")
            layout.addWidget(self.project_combo, 3, 1)

            layout.addWidget(QLabel("<b>Client</b><font color='#FF6B6B'>*</font><b>:</b>"), 4, 0)
            self.topic_input = QLineEdit()
            self.topic_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.topic_input, 4, 1)

            layout.addWidget(QLabel("<b>DND Template</b><b>:</b>"), 5, 0)
            self.dnd_template_list = QListWidget()
            self.dnd_template_list.setStyleSheet("background-color: #dde2e8;")
            self.dnd_template_list.setWordWrap(True)
            self.dnd_template_list.setContextMenuPolicy(Qt.CustomContextMenu)
            self.dnd_template_list.customContextMenuRequested.connect(self.show_template_context_menu)
            self.update_templates(self.dnd_templates)
            self.dnd_template_list.setSelectionMode(QListWidget.SingleSelection)
            self.dnd_template_list.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            self.dnd_template_list.currentItemChanged.connect(self.on_template_selected)
            self.dnd_template_list.itemClicked.connect(self.on_template_clicked)
            layout.addWidget(self.dnd_template_list, 5, 1)
            item_height = self.dnd_template_list.sizeHintForRow(0)
            self.dnd_template_list.setMaximumHeight(item_height * 5 + 2)
            self.dnd_template_list.clearSelection()

            layout.addWidget(QLabel("<b>Template Name</b><b>:</b>"), 6, 0)
            self.template_name_input = QLineEdit()
            self.template_name_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.template_name_input, 6, 1)
            self.save_template_btn = QPushButton("Save Template")
            self.save_template_btn.clicked.connect(lambda: self.window().save_template_from_input(self, 'dnd'))
            layout.addWidget(self.save_template_btn, 6, 2)
            self.delete_template_btn = QPushButton("Delete Template")
            self.delete_template_btn.clicked.connect(lambda: self.window().delete_template(self, 'dnd'))
            layout.addWidget(self.delete_template_btn, 6, 3)

            self.with_list.itemSelectionChanged.connect(self.update_selected_users_label)
            self.search_input.textChanged.connect(self.filter_users)
            self.on_customer_changed()
            self.with_list.installEventFilter(self)
            self.dnd_template_list.installEventFilter(self)
        except Exception:
            traceback.print_exc()

    def on_template_clicked(self, item):
        try:
            self.dnd_template_list.setCurrentItem(item)
            self.on_template_selected(item, None)
        except Exception:
            traceback.print_exc()        

    def eventFilter(self, obj, event):
        if obj == self.with_list and event.type() == QEvent.KeyPress:
            key = event.key()
            if Qt.Key_A <= key <= Qt.Key_Z:
                self.search_input.setFocus()
                self.search_input.setText(event.text())
                self.search_input.selectAll()
                return True
        elif obj == self.dnd_template_list and event.type() == QEvent.KeyPress:
            key = event.key()
            if Qt.Key_A <= key <= Qt.Key_Z:
                letter = event.text().lower()
                for i in range(self.dnd_template_list.count()):
                    item = self.dnd_template_list.item(i)
                    name = item.text()[2:].lower()
                    if name.startswith(letter):
                        self.dnd_template_list.setCurrentRow(i)
                        self.dnd_template_list.scrollToItem(item)
                        break
                return True
        return super().eventFilter(obj, event)

    def get_customer_list(self):
        try:
            user_data = self.dict_UserData.get(self.user)
            return user_data.get("List_Of_Customer", []) if user_data else []
        except Exception:
            traceback.print_exc()
            return []

    def populate_with_list(self):
        try:
            self.with_list.clear()
            seen_names = set()
            user_items = []
            for key, user_data in self.dict_UserData.items():
                full_name = user_data.get("fullName", key)
                if full_name not in seen_names:
                    seen_names.add(full_name)
                    user_items.append((key, full_name))
            normal_users = [(key, name) for key, name in user_items if not name.startswith("--")]
            special_users = [(key, name) for key, name in user_items if name.startswith("--")]
            normal_users.sort(key=lambda x: (-(x[1] in self.favorite_users), x[1].lower()))
            special_users.sort(key=lambda x: (-(x[1] in self.favorite_users), x[1].lower()))
            sorted_users = normal_users + special_users
            for key, full_name in sorted_users:
                prefix = "★ " if full_name in self.favorite_users else "☆ "
                item = QListWidgetItem(prefix + full_name)
                item.setData(Qt.UserRole, key)
                self.with_list.addItem(item)
            self.with_list.setSelectionMode(QListWidget.MultiSelection)
        except Exception:
            traceback.print_exc()

    def filter_users(self, text):
        try:
            text = text.lower()
            for i in range(self.with_list.count()):
                item = self.with_list.item(i)
                full_name = item.text()[2:].lower()
                names = full_name.split()
                first_name = names[0] if names else ""
                last_name = names[-1] if len(names) > 1 else ""
                item.setHidden(not (first_name.startswith(text) or last_name.startswith(text)))
        except Exception:
            traceback.print_exc()

    def select_first_visible_user(self):
        try:
            for i in range(self.with_list.count()):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(True)
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_next_visible_user(self):
        try:
            current_row = self.with_list.currentRow()
            for i in range(current_row + 1, self.with_list.count()):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(not item.isSelected())
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_previous_visible_user(self):
        try:
            current_row = self.with_list.currentRow()
            for i in range(current_row - 1, -1, -1):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(not item.isSelected())
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_user_selection(self, item):
        try:
            item.setSelected(not item.isSelected())
            self.update_selected_users_label()
        except Exception:
            traceback.print_exc()

    def keyPressEvent(self, event):
        try:
            if self.with_list.hasFocus() and event.text().isalpha():
                letter = event.text().lower()
                for i in range(self.with_list.count()):
                    item = self.with_list.item(i)
                    name = item.text()[2:].strip()
                    if name.lower().startswith(letter):
                        self.with_list.setCurrentRow(i)
                        self.with_list.scrollToItem(item)
                        break
                event.accept()
                return
            if event.key() in (Qt.Key_Return, Qt.Key_Enter):
                if self.search_input.hasFocus() and self.search_input.text().strip():
                    self.select_first_visible_user()
                    self.with_list.setFocus()
                elif self.with_list.hasFocus():
                    current_item = self.with_list.currentItem()
                    if current_item and not current_item.isHidden():
                        self.toggle_user_selection(current_item)
                elif self.save_template_btn.hasFocus():
                    self.save_template_btn.click()
            elif event.key() == Qt.Key_Down and self.with_list.hasFocus():
                self.toggle_next_visible_user()
            elif event.key() == Qt.Key_Up and self.with_list.hasFocus():
                self.toggle_previous_visible_user()
            super().keyPressEvent(event)
        except Exception:
            traceback.print_exc()

    def update_selected_users_label(self):
        try:
            selected_keys = [item.data(Qt.UserRole) for item in self.with_list.selectedItems()]
            selected_names = [self.dict_UserData[key].get("fullName", key) for key in selected_keys]
            self.selected_users_value.setText(", ".join(selected_names) if selected_names else "None")
        except Exception:
            traceback.print_exc()

    def on_customer_changed(self):
        try:
            selected_customer = self.customer_combo.currentText()
            self.project_combo.clear()
            project_list = sorted(self.dictCustomerData.get(selected_customer, []))
            self.project_combo.addItems(project_list if project_list else ["Select Project"])
        except Exception:
            traceback.print_exc()

    def on_template_selected(self, current, previous):
        try:
            if current:
                template_name = current.data(Qt.UserRole)
                data = self.dnd_templates.get(template_name, {})
                customer = data.get("customer", "")
                customer_index = self.customer_combo.findText(customer)
                if customer_index >= 0:
                    self.customer_combo.setCurrentIndex(customer_index)
                project = data.get("project", "")
                project_index = self.project_combo.findText(project)
                if project_index >= 0:
                    self.project_combo.setCurrentIndex(project_index)
                topic = data.get("topic", "")
                self.topic_input.setText(topic)
                with_users = data.get("with", [])
                self.with_list.clearSelection()
                for full_name in with_users:
                    for i in range(self.with_list.count()):
                        item = self.with_list.item(i)
                        if item.text()[2:].strip() == full_name:
                            item.setSelected(True)
                            break
                self.update_selected_users_label()
        except Exception:
            traceback.print_exc()

    def update_templates(self, templates):
        try:
            self.dnd_templates = templates
            current_item = self.dnd_template_list.currentItem()
            current_name = current_item.data(Qt.UserRole) if current_item else None
            self.dnd_template_list.clear()
            sorted_templates = sorted(templates.items(), key=lambda x: (-x[1].get("favorite", False), x[0].lower()))
            for name, data in sorted_templates:
                prefix = "★ " if data.get("favorite", False) else "☆ "
                item = QListWidgetItem(prefix + name)
                item.setData(Qt.UserRole, name)
                self.dnd_template_list.addItem(item)
            if current_name:
                for i in range(self.dnd_template_list.count()):
                    if self.dnd_template_list.item(i).data(Qt.UserRole) == current_name:
                        self.dnd_template_list.setCurrentRow(i)
                        break
            else:
                self.dnd_template_list.setCurrentRow(0 if self.dnd_template_list.count() > 0 else -1)
            if self.dnd_template_list.count() > 0:
                item_height = self.dnd_template_list.sizeHintForRow(0)
                self.dnd_template_list.setMaximumHeight(item_height * min(5, self.dnd_template_list.count()) + 2)
            else:
                self.dnd_template_list.setMaximumHeight(100)
        except Exception:
            traceback.print_exc()

    def clear(self):
        try:
            self.with_list.clearSelection()
            self.customer_combo.setCurrentIndex(0)
            self.project_combo.clear()
            self.project_combo.addItem("Select Project")
            self.dnd_template_list.clearSelection()
            self.template_name_input.clear()
            self.search_input.clear()
            self.topic_input.clear()
            self.reset_highlights()
            self.selected_users_value.setText("None")
        except Exception:
            traceback.print_exc()

    def highlight_field(self, widget):
        try:
            widget.setStyleSheet("border: 2px solid #FF6B6B; background-color: #F8F9FA; color: #343A40;")
        except Exception:
            traceback.print_exc()

    def reset_highlight(self, widget):
        try:
            widget.setStyleSheet("border: 1px solid #CED4DA; background-color: #F8F9FA; color: #343A40;")
        except Exception:
            traceback.print_exc()

    def reset_highlights(self):
        try:
            self.reset_highlight(self.with_list)
            self.reset_highlight(self.customer_combo)
            self.reset_highlight(self.project_combo)
            self.reset_highlight(self.topic_input)
        except Exception:
            traceback.print_exc()

    def validate(self):
        try:
            is_valid = True
            # if not self.with_list.selectedItems():
            #     self.highlight_field(self.with_list)
            #     is_valid = False
            # else:
            #     self.reset_highlight(self.with_list)
            if self.customer_combo.currentText() == "Select Customer":
                self.highlight_field(self.customer_combo)
                is_valid = False
            else:
                self.reset_highlight(self.customer_combo)
            if self.project_combo.currentText() == "Select Project":
                self.highlight_field(self.project_combo)
                is_valid = False
            else:
                self.reset_highlight(self.project_combo)
            if not self.topic_input.text().strip():
                self.highlight_field(self.topic_input)
                is_valid = False
            else:
                self.reset_highlight(self.topic_input)
            return is_valid
        except Exception:
            traceback.print_exc()
            return False

    def get_selected_user_keys(self):
        try:
            return [item.data(Qt.UserRole) for item in self.with_list.selectedItems()]
        except Exception:
            traceback.print_exc()
            return []

    def show_user_context_menu(self, position):
        try:
            item = self.with_list.itemAt(position)
            if item:
                key = item.data(Qt.UserRole)
                full_name = self.dict_UserData[key].get("fullName", key)
                is_favorite = full_name in self.favorite_users
                menu = QMenu()
                action_text = "Remove from Favorites" if is_favorite else "Add to Favorites"
                action = menu.addAction(action_text)
                action.triggered.connect(lambda: self.toggle_user_favorite(key))
                menu.exec_(self.with_list.mapToGlobal(position))
        except Exception:
            traceback.print_exc()

    def toggle_user_favorite(self, key):
        try:
            full_name = self.dict_UserData[key].get("fullName", key)
            if full_name in self.favorite_users:
                self.favorite_users.remove(full_name)
            else:
                self.favorite_users.add(full_name)
            os.makedirs("favorites", exist_ok=True)
            with open(f"favorites/{self.user}_favorites.json", "w") as f:
                json.dump(list(self.favorite_users), f)
            self.window().update_all_with_lists()
        except Exception:
            traceback.print_exc()

    def show_template_context_menu(self, position):
        try:
            item = self.dnd_template_list.itemAt(position)
            if item:
                name = item.data(Qt.UserRole)
                is_favorite = self.dnd_templates[name].get("favorite", False)
                menu = QMenu()
                action_text = "Remove from Favorites" if is_favorite else "Add to Favorites"
                action = menu.addAction(action_text)
                action.triggered.connect(lambda: self.toggle_template_favorite(name))
                menu.exec_(self.dnd_template_list.mapToGlobal(position))
        except Exception:
            traceback.print_exc()

    def toggle_template_favorite(self, name):
        try:
            if name in self.dnd_templates:
                self.dnd_templates[name]["favorite"] = not self.dnd_templates[name].get("favorite", False)
                os.makedirs("templates", exist_ok=True)
                file_path = f"templates/{self.user}_dnd_template.json"
                with open(file_path, "w") as f:
                    json.dump(self.dnd_templates, f, indent=4)
                self.window().update_all_dnd_inputs()
        except Exception:
            traceback.print_exc()

class HalfDayLeaveInput(QFrame):
    def __init__(self, user, dict_UserData, dictCustomerData, allowed_customers, favorite_users, parent=None):
        super().__init__(parent)
        try:
            self.user = user
            self.dict_UserData = dict_UserData
            self.dictCustomerData = dictCustomerData
            self.allowed_customers = allowed_customers
            self.favorite_users = favorite_users

            self.setObjectName("HalfDayLeaveInput")
            layout = QGridLayout(self)
            layout.setSpacing(10)
            layout.setContentsMargins(10, 10, 10, 10)

            # Approved by field (similar to Meeting's "With" field)
            layout.addWidget(QLabel("<b>Approved by</b><font color='#FF6B6B'>*</font><b>:</b>"), 0, 0)
            with_layout = QVBoxLayout()
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Search users...")
            self.search_input.setStyleSheet("background-color: #dde2e8;")
            with_layout.addWidget(self.search_input)
            self.with_list = QListWidget()
            self.with_list.setStyleSheet("background-color: #dde2e8;")
            self.with_list.setContextMenuPolicy(Qt.CustomContextMenu)
            self.with_list.customContextMenuRequested.connect(self.show_user_context_menu)
            self.populate_with_list()
            with_layout.addWidget(self.with_list)
            layout.addLayout(with_layout, 0, 1, 1, 3)

            layout.addWidget(QLabel("<b>Selected Users:</b>"), 1, 0)
            self.selected_users_value = QLabel("None")
            self.selected_users_value.setStyleSheet("color: #343A40; font-size: 16px;")
            layout.addWidget(self.selected_users_value, 1, 1)

            # Notes field
            layout.addWidget(QLabel("<b>Notes</b><font color='#FF6B6B'>*</font><b>:</b>"), 2, 0)
            self.notes_input = QLineEdit()
            self.notes_input.setPlaceholderText("Enter notes about your half day leave")
            self.notes_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.notes_input, 2, 1)

            self.with_list.itemSelectionChanged.connect(self.update_selected_users_label)
            self.search_input.textChanged.connect(self.filter_users)
            self.with_list.installEventFilter(self)
        except Exception:
            traceback.print_exc()

    def eventFilter(self, obj, event):
        if obj == self.with_list and event.type() == QEvent.KeyPress:
            key = event.key()
            if Qt.Key_A <= key <= Qt.Key_Z:
                self.search_input.setFocus()
                self.search_input.setText(event.text())
                self.search_input.selectAll()
                return True
        return super().eventFilter(obj, event)

    def populate_with_list(self):
        try:
            self.with_list.clear()
            seen_names = set()
            user_items = []
            for key, user_data in self.dict_UserData.items():
                full_name = user_data.get("fullName", key)
                if full_name not in seen_names:
                    seen_names.add(full_name)
                    user_items.append((key, full_name))
            normal_users = [(key, name) for key, name in user_items if not name.startswith("--")]
            special_users = [(key, name) for key, name in user_items if name.startswith("--")]
            normal_users.sort(key=lambda x: (-(x[1] in self.favorite_users), x[1].lower()))
            special_users.sort(key=lambda x: (-(x[1] in self.favorite_users), x[1].lower()))
            sorted_users = normal_users + special_users
            for key, full_name in sorted_users:
                prefix = "★ " if full_name in self.favorite_users else "☆ "
                item = QListWidgetItem(prefix + full_name)
                item.setData(Qt.UserRole, key)
                self.with_list.addItem(item)
            self.with_list.setSelectionMode(QListWidget.MultiSelection)
        except Exception:
            traceback.print_exc()

    def filter_users(self, text):
        try:
            text = text.lower()
            for i in range(self.with_list.count()):
                item = self.with_list.item(i)
                full_name = item.text()[2:].lower()
                names = full_name.split()
                first_name = names[0] if names else ""
                last_name = names[-1] if len(names) > 1 else ""
                item.setHidden(not (first_name.startswith(text) or last_name.startswith(text)))
        except Exception:
            traceback.print_exc()

    def select_first_visible_user(self):
        try:
            for i in range(self.with_list.count()):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(True)
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_next_visible_user(self):
        try:
            current_row = self.with_list.currentRow()
            for i in range(current_row + 1, self.with_list.count()):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(not item.isSelected())
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_previous_visible_user(self):
        try:
            current_row = self.with_list.currentRow()
            for i in range(current_row - 1, -1, -1):
                item = self.with_list.item(i)
                if not item.isHidden():
                    item.setSelected(not item.isSelected())
                    self.with_list.setCurrentItem(item)
                    self.update_selected_users_label()
                    break
        except Exception:
            traceback.print_exc()

    def toggle_user_selection(self, item):
        try:
            item.setSelected(not item.isSelected())
            self.update_selected_users_label()
        except Exception:
            traceback.print_exc()

    def keyPressEvent(self, event):
        try:
            if self.with_list.hasFocus() and event.text().isalpha():
                letter = event.text().lower()
                for i in range(self.with_list.count()):
                    item = self.with_list.item(i)
                    name = item.text()[2:].strip()
                    if name.lower().startswith(letter):
                        self.with_list.setCurrentRow(i)
                        self.with_list.scrollToItem(item)
                        break
                event.accept()
                return
            if event.key() in (Qt.Key_Return, Qt.Key_Enter):
                if self.search_input.hasFocus() and self.search_input.text().strip():
                    self.select_first_visible_user()
                    self.with_list.setFocus()
                elif self.with_list.hasFocus():
                    current_item = self.with_list.currentItem()
                    if current_item and not current_item.isHidden():
                        self.toggle_user_selection(current_item)
            elif event.key() == Qt.Key_Down and self.with_list.hasFocus():
                self.toggle_next_visible_user()
            elif event.key() == Qt.Key_Up and self.with_list.hasFocus():
                self.toggle_previous_visible_user()
            super().keyPressEvent(event)
        except Exception:
            traceback.print_exc()

    def update_selected_users_label(self):
        try:
            selected_keys = [item.data(Qt.UserRole) for item in self.with_list.selectedItems()]
            selected_names = [self.dict_UserData[key].get("fullName", key) for key in selected_keys]
            self.selected_users_value.setText(", ".join(selected_names) if selected_names else "None")
        except Exception:
            traceback.print_exc()

    def clear(self):
        try:
            self.with_list.clearSelection()
            self.notes_input.clear()
            self.search_input.clear()
            self.reset_highlights()
            self.selected_users_value.setText("None")
        except Exception:
            traceback.print_exc()

    def highlight_field(self, widget):
        try:
            widget.setStyleSheet("border: 2px solid #FF6B6B; background-color: #F8F9FA; color: #343A40;")
        except Exception:
            traceback.print_exc()

    def reset_highlight(self, widget):
        try:
            widget.setStyleSheet("border: 1px solid #CED4DA; background-color: #F8F9FA; color: #343A40;")
        except Exception:
            traceback.print_exc()

    def reset_highlights(self):
        try:
            self.reset_highlight(self.with_list)
            self.reset_highlight(self.notes_input)
        except Exception:
            traceback.print_exc()

    def validate(self):
        try:
            is_valid = True
            if not self.with_list.selectedItems():
                self.highlight_field(self.with_list)
                is_valid = False
            else:
                self.reset_highlight(self.with_list)
            if not self.notes_input.text().strip():
                self.highlight_field(self.notes_input)
                is_valid = False
            else:
                self.reset_highlight(self.notes_input)
            return is_valid
        except Exception:
            traceback.print_exc()
            return False

    def get_selected_user_keys(self):
        try:
            return [item.data(Qt.UserRole) for item in self.with_list.selectedItems()]
        except Exception:
            traceback.print_exc()
            return []

    def show_user_context_menu(self, position):
        try:
            item = self.with_list.itemAt(position)
            if item:
                key = item.data(Qt.UserRole)
                full_name = self.dict_UserData[key].get("fullName", key)
                is_favorite = full_name in self.favorite_users
                menu = QMenu()
                action_text = "Remove from Favorites" if is_favorite else "Add to Favorites"
                action = menu.addAction(action_text)
                action.triggered.connect(lambda: self.toggle_user_favorite(key))
                menu.exec_(self.with_list.mapToGlobal(position))
        except Exception:
            traceback.print_exc()

    def toggle_user_favorite(self, key):
        try:
            full_name = self.dict_UserData[key].get("fullName", key)
            if full_name in self.favorite_users:
                self.favorite_users.remove(full_name)
            else:
                self.favorite_users.add(full_name)
            os.makedirs("favorites", exist_ok=True)
            with open(f"favorites/{self.user}_favorites.json", "w") as f:
                json.dump(list(self.favorite_users), f)
            self.window().update_all_with_lists()
        except Exception:
            traceback.print_exc()

class BreakEntry(QFrame):
    def __init__(self, user, dictUsersData, dictCustomerData, meeting_templates, allowed_customers, dnd_templates, favorite_users, initial_time="0", is_dnd_fixed=False, is_half_day_leave=False, parent=None):
        super().__init__(parent)
        try:
            self.user = user
            self.dictUsersData = dictUsersData
            self.dictCustomerData = dictCustomerData
            self.meeting_templates = meeting_templates
            self.allowed_customers = allowed_customers
            self.favorite_users = favorite_users
            self.dnd_templates = dnd_templates
            self.is_dnd_fixed = is_dnd_fixed # Flag to mark fixed DND entry
            self.is_half_day_leave = is_half_day_leave # Flag to mark half day leave entry

            self.setFrameStyle(QFrame.Box | QFrame.Plain)
            self.setLineWidth(1)
            self.setContentsMargins(10, 10, 10, 10)

            layout = QVBoxLayout(self)

            type_time_layout = QHBoxLayout()
            self.break_type_combo = QComboBox()
            self.break_type_combo.addItems(["Break", "Meeting", "Interview", "System Down", "Working On Other System", "DND Customer", "Half Day Leave"])
            if self.is_dnd_fixed:
                self.break_type_combo.setCurrentText("DND Customer")
                self.break_type_combo.setEnabled(False)  # Disable type changes for fixed DND
            elif self.is_half_day_leave:
                self.break_type_combo.setCurrentText("Half Day Leave")
                self.break_type_combo.setEnabled(False)
            self.break_type_combo.currentIndexChanged.connect(self.on_break_type_changed)
            type_time_layout.addWidget(QLabel("<b>Break type:</b>"))
            type_time_layout.addWidget(self.break_type_combo)
            type_time_layout.addWidget(QLabel("<b>Estimated time:</b><font color='#FF6B6B'>*</font>"))
            self.time_input = QLineEdit(initial_time)
            self.time_input.setFixedWidth(60)
            self.time_input.setStyleSheet("background-color: #dde2e8; border: 1px solid #8a929b;")
            # if self.is_dnd_fixed:
            #     self.time_input.setReadOnly(True)  # Make time read-only for fixed DND
            # if self.is_half_day_leave:
                # self.time_input.setReadOnly(True)  # Make time read-only for half-day leave
            type_time_layout.addWidget(self.time_input)
            type_time_layout.addWidget(QLabel("<b>min</b>"))
            layout.addLayout(type_time_layout)

            self.stacked_widget = QStackedWidget()
            self.break_frame = self.create_break_frame()
            self.meeting_frame = self.create_meeting_frame()
            self.interview_frame = self.create_interview_frame()
            self.system_down_frame = self.create_system_down_frame()
            self.working_system_frame = self.create_working_system_frame()
            self.dnd_frame = self.create_dnd_frame()
            self.half_day_leave_frame = self.create_half_day_leave_frame()
            self.stacked_widget.addWidget(self.break_frame)
            self.stacked_widget.addWidget(self.meeting_frame)
            self.stacked_widget.addWidget(self.interview_frame)
            self.stacked_widget.addWidget(self.system_down_frame)
            self.stacked_widget.addWidget(self.working_system_frame)
            self.stacked_widget.addWidget(self.dnd_frame)
            self.stacked_widget.addWidget(self.half_day_leave_frame)
            layout.addWidget(self.stacked_widget)
            if is_dnd_fixed:
                self.break_type_combo.setCurrentText("DND Customer")
            elif is_half_day_leave:
                self.break_type_combo.setCurrentText("Half Day Leave")
                # For Half Day Leave, set time input to empty and make it editable
                self.time_input.setText("0")
                self.time_input.setReadOnly(False)
            else:
                self.break_type_combo.setCurrentText("Break")
            self.on_break_type_changed()
        except Exception:
            traceback.print_exc()

    def create_break_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            placeholder = QLabel("No additional input required for Break.")
            placeholder.setStyleSheet("font-weight: bold;")
            placeholder.setAlignment(Qt.AlignCenter)
            layout.addWidget(placeholder)
            return frame
        except Exception:
            traceback.print_exc()

    def create_meeting_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            self.meeting_input = MeetingInput(self.user, self.dictUsersData, self.dictCustomerData, self.meeting_templates, self.allowed_customers, self.favorite_users)
            layout.addWidget(self.meeting_input)
            return frame
        except Exception:
            traceback.print_exc()

    def create_interview_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            self.interview_input = InterviewInput(self.dictCustomerData, self.allowed_customers)
            layout.addWidget(self.interview_input)
            return frame
        except Exception:
            traceback.print_exc()

    def create_system_down_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            placeholder = QLabel("System Down. No additional details required.")
            placeholder.setStyleSheet("font-weight: bold;")
            placeholder.setAlignment(Qt.AlignCenter)
            layout.addWidget(placeholder)
            return frame
        except Exception:
            traceback.print_exc()

    def create_working_system_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            placeholder = QLabel("Working On Other System. No additional details required.")
            placeholder.setStyleSheet("font-weight: bold;")
            placeholder.setAlignment(Qt.AlignCenter)
            layout.addWidget(placeholder)
            return frame
        except Exception:
            traceback.print_exc()

    def create_dnd_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            self.dnd_input = DndInput(self.user, self.dictUsersData, self.dictCustomerData, self.dnd_templates, self.allowed_customers, self.favorite_users)
            layout.addWidget(self.dnd_input)
            return frame
        except Exception:
            traceback.print_exc()

    def create_half_day_leave_frame(self):
        try:
            frame = QFrame()
            layout = QVBoxLayout(frame)
            self.half_day_leave_input = HalfDayLeaveInput(self.user, self.dictUsersData, self.dictCustomerData, self.allowed_customers, self.favorite_users)
            layout.addWidget(self.half_day_leave_input)
            return frame
        except Exception:
            traceback.print_exc()

    def on_break_type_changed(self):
        try:
            type_text = self.break_type_combo.currentText()
            frame_map = {
                "Break": self.break_frame,
                "Meeting": self.meeting_frame,
                "Interview": self.interview_frame,
                "System Down": self.system_down_frame,
                "Working On Other System": self.working_system_frame,
                "DND Customer": self.dnd_frame,
                "Half Day Leave": self.half_day_leave_frame
            }
            self.stacked_widget.setCurrentWidget(frame_map.get(type_text, self.break_frame))

            # For Half Day Leave, make the time input editable and clear it if it's 0
            if type_text == "Half Day Leave":
                self.time_input.setReadOnly(False)
                if self.time_input.text() == "0":
                    self.time_input.setText("")
                # Update the window's time inputs to reflect this change
                if hasattr(self, 'window') and callable(self.window) and hasattr(self.window(), 'update_time_inputs'):
                    self.window().update_time_inputs()
            else:
                # For other types, follow the normal behavior
                if hasattr(self, 'window') and callable(self.window) and hasattr(self.window(), 'break_entries'):
                    if len(self.window().break_entries) == 1:
                        self.time_input.setReadOnly(True)
                        # Set the time to the actual time for single entries that are not Half Day Leave
                        if hasattr(self.window(), 'minutes'):
                            self.time_input.setText(str(self.window().minutes))
                    elif self == self.window().break_entries[-1]:
                        self.time_input.setReadOnly(True)
                    else:
                        self.time_input.setReadOnly(False)
                    # Update the window's time inputs to reflect this change
                    if hasattr(self.window(), 'update_time_inputs'):
                        self.window().update_time_inputs()
        except Exception:
            traceback.print_exc()

    def clear(self):
        try:
            self.break_type_combo.setCurrentIndex(0)
            self.time_input.setText("0")
            if hasattr(self, 'meeting_input'):
                self.meeting_input.clear()
            if hasattr(self, 'interview_input'):
                self.interview_input.clear()
            if hasattr(self, 'dnd_input'):
                self.dnd_input.clear()
            if hasattr(self, 'half_day_leave_input'):
                self.half_day_leave_input.clear()
        except Exception:
            traceback.print_exc()

    def validate(self):
        try:
            break_type = self.break_type_combo.currentText()
            time_text = self.time_input.text().strip()

            # For Half Day Leave, time is required but can be any positive number
            if break_type == "Half Day Leave":
                if not time_text or not time_text.isdigit() or int(time_text) <= 0:
                    self.time_input.setStyleSheet("border: 2px solid #FF6B6B; background-color: #dde2e8;")
                    return False, "Estimated time must be a valid positive number for Half Day Leave."
            # For other break types, validate as before
            elif not time_text or not time_text.isdigit() or int(time_text) < 0:
                self.time_input.setStyleSheet("border: 2px solid #FF6B6B; background-color: #dde2e8;")
                return False, "Estimated time must be a valid Integer."

            self.time_input.setStyleSheet("border: 1px solid #CED4DA; background-color: #dde2e8;")

            if break_type == "Meeting":
                if not self.meeting_input.validate():
                    return False, "Please fill all required Meeting fields."
            elif break_type == "Interview":
                if not self.interview_input.validate():
                    return False, "Please fill all required Interview fields."
            elif break_type == "DND Customer":
                if not self.dnd_input.validate():
                    return False, "Please fill all required DND fields."
            elif break_type == "Half Day Leave":
                if not self.half_day_leave_input.validate():
                    return False, "Please fill all required Half Day Leave fields."
            return True, ""
        except Exception:
            traceback.print_exc()
            return False, "Validation failed due to error."

    def get_data(self):
        """Collect data from the entry, using original keys for all break types."""
        try:
            break_type = self.break_type_combo.currentText()
            time_text = self.time_input.text().strip()
            data = {
                "BreakType": break_type,
                "strTimeInMin": time_text,
                "Meeting_with": [],
                "Meeting_Project": "",
                "Meeting_Customer": "",
                "Meeting_Topic": "",
                "DND_with": [],
                "DND_Project": "",
                "DND_Customer": "",
                "DND_Topic": self.dnd_input.topic_input.text() if hasattr(self.dnd_input, 'topic_input') else "",
                "Interview_CandidateName": "",
                "Interview_Position": "",
                "Interview_Team": "",
                "Interview_Customer": "",
                "HalfDayLeave_ApprovedBy": [],
                "HalfDayLeave_Notes": ""
            }
            if break_type == "Meeting":
                data["Meeting_with"] = [
                    item.text().lstrip("☆★").strip()
                    for item in self.meeting_input.with_list.selectedItems()
                ]
                data["Meeting_Project"] = self.meeting_input.project_combo.currentText()
                data["Meeting_Customer"] = self.meeting_input.customer_combo.currentText()
                data["Meeting_Topic"] = self.meeting_input.topic_input.text().strip()
            elif break_type == "Interview":
                data["Interview_CandidateName"] = self.interview_input.candidate_input.text()
                data["Interview_Position"] = self.interview_input.position_input.text()
                data["Interview_Team"] = self.interview_input.team_combo.currentText()
                data["Interview_Customer"] = self.interview_input.customer_combo.currentText()
            elif break_type == "DND Customer":
                # Use same keys as Meeting to match original structure
                data["DND_with"] = [
                    item.text().lstrip("☆★").strip()
                    for item in self.dnd_input.with_list.selectedItems()
                ]
                data["DND_Project"] = self.dnd_input.project_combo.currentText()
                data["DND_Customer"] = self.dnd_input.customer_combo.currentText()
                data["DND_Topic"] = self.dnd_input.topic_input.text().strip()
            elif break_type == "Half Day Leave":
                data["HalfDayLeave_ApprovedBy"] = [
                    item.text().lstrip("☆★").strip()
                    for item in self.half_day_leave_input.with_list.selectedItems()
                ]
                data["HalfDayLeave_Notes"] = self.half_day_leave_input.notes_input.text().strip()
            return data
        except Exception:
            traceback.print_exc()
            return {}

    def set_time_input_readonly(self, readonly):
        try:
            self.time_input.setReadOnly(readonly)
            if readonly:
                self.time_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #e9ecef;
                        color: #495057;
                        border: 1px solid #CED4DA;
                        padding: 2px;
                    }
                    QLineEdit:focus {
                        border: 2px solid #495057;
                    }
                """)
            else:
                self.time_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #dde2e8;
                        color: #2c2f33;
                        border: 1px solid #CED4DA;
                        padding: 2px;
                    }
                    QLineEdit:focus {
                        border: 2px solid #495057;
                    }
                """)
        except Exception:
            traceback.print_exc()

class InterviewInput(QFrame):
    def __init__(self, dictCustomerData, allowed_customers, parent=None):
        super().__init__(parent)
        try:
            self.dictCustomerData = dictCustomerData
            self.allowed_customers = allowed_customers
            self.setObjectName("InterviewInput")
            layout = QGridLayout(self)
            layout.setSpacing(10)
            layout.setContentsMargins(10, 10, 10, 10)

            layout.addWidget(QLabel("<b>Candidate Name</b><font color='#FF6B6B'>*</font><b>:</b>"), 0, 0)
            self.candidate_input = QLineEdit()
            self.candidate_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.candidate_input, 0, 1)

            layout.addWidget(QLabel("<b>Team</b><font color='#FF6B6B'>*</font><b>:</b>"), 0, 2)
            self.team_combo = QComboBox()
            self.team_combo.addItems(["Accountant", "CRM", "Developer", "General", "HR"])
            self.team_combo.setStyleSheet("QComboBox { background-color: #dde2e8; }")
            self.team_combo.setMinimumWidth(200)
            layout.addWidget(self.team_combo, 0, 3)

            layout.addWidget(QLabel("<b>Position</b><font color='#FF6B6B'>*</font><b>:</b>"), 1, 0)
            self.position_input = QLineEdit()
            self.position_input.setStyleSheet("background-color: #dde2e8;")
            layout.addWidget(self.position_input, 1, 1)

            layout.addWidget(QLabel("<b>Customer</b><font color='#FF6B6B'>*</font><b>:</b>"), 1, 2)
            self.customer_combo = QComboBox()
            self.customer_combo.addItems(sorted([cust for cust in self.allowed_customers if cust in self.dictCustomerData]))
            self.customer_combo.setStyleSheet("QComboBox { background-color: #dde2e8; }")
            self.customer_combo.setMinimumWidth(200)
            layout.addWidget(self.customer_combo, 1, 3)
        except Exception:
            traceback.print_exc()

    def clear(self):
        try:
            self.candidate_input.clear()
            self.team_combo.setCurrentIndex(-1)
            self.position_input.clear()
            self.customer_combo.setCurrentIndex(-1)
            self.clear_highlights()
        except Exception:
            traceback.print_exc()

    def copy_values_from(self, original):
        try:
            self.candidate_input.setText(original.candidate_input.text())
            self.team_combo.setCurrentText(original.team_combo.currentText())
            self.position_input.setText(original.position_input.text())
            self.customer_combo.setCurrentText(original.customer_combo.currentText())
        except Exception:
            traceback.print_exc()

    def highlight_field(self, widget):
        try:
            widget.setStyleSheet("border: 2px solid #FF6B6B; border-radius: 5px; padding: 3px; background-color: #F8F9FA; color: #343A40; font-size: 16px;")
        except Exception:
            traceback.print_exc()

    def clear_highlight(self, widget):
        try:
            widget.setStyleSheet("border: 1px solid #CED4DA; border-radius: 5px; padding: 3px; background-color: #F8F9FA; color: #343A40; font-size: 16px;")
        except Exception:
            traceback.print_exc()

    def clear_highlights(self):
        try:
            self.clear_highlight(self.candidate_input)
            self.clear_highlight(self.team_combo)
            self.clear_highlight(self.position_input)
            self.clear_highlight(self.customer_combo)
        except Exception:
            traceback.print_exc()

    def validate(self):
        try:
            is_valid = True
            if not self.candidate_input.text().strip():
                self.highlight_field(self.candidate_input)
                is_valid = False
            else:
                self.clear_highlight(self.candidate_input)
            if self.team_combo.currentIndex() == -1:
                self.highlight_field(self.team_combo)
                is_valid = False
            else:
                self.clear_highlight(self.team_combo)
            if not self.position_input.text().strip():
                self.highlight_field(self.position_input)
                is_valid = False
            else:
                self.clear_highlight(self.position_input)
            if self.customer_combo.currentIndex() == -1:
                self.highlight_field(self.customer_combo)
                is_valid = False
            else:
                self.clear_highlight(self.customer_combo)
            return is_valid
        except Exception:
            traceback.print_exc()
            return False

class BreakPopUp(QDialog):
    def __init__(self, minutes, user, dicMonConfig, dictUsersData, dictGeneralAccount, dictCustomerData, after_dnd=False, is_half_day_leave=False):
        super().__init__()
        try:
            self.minutes = int(minutes)
            self.user = user
            self.dict_config = dicMonConfig
            self.dictUsersData = dictUsersData
            self.dictCustomerData = dictCustomerData
            self.after_dnd = after_dnd
            self.is_half_day_leave = is_half_day_leave
            self.dictGeneralAccount = dictGeneralAccount
            self.allowed_customers = self.dictUsersData.get(self.user, {}).get("List_Of_Customer", [])
            self.meeting_templates = {}
            self.dnd_templates = {}
            self.favorite_users = set()
            self.meeting_inputs = []
            self.break_entries = []
            self.dnd_inputs = []
            self.half_day_leave_inputs = []
            try:
                with open(f"favorites/{self.user}_favorites.json", "r") as f:
                    self.favorite_users = set(json.load(f))
            except FileNotFoundError:
                pass
            self.current_login_name = self.user
            self.result = None
            self.color_list = [
                "#c5cfd9", "#F0F8FF", "#FAEBD7", "#D6EAF8", "#F5F5DC",
                "#FFE4C4", "#76D7C4", "#FFF9C4", "#FFE082", "#FFB74D", "#FF8A65"
            ]
            self.setWindowTitle(self.dict_config["Version"])
            self.setMinimumSize(900, 900)
            self.setWindowFlags(Qt.Window | Qt.WindowTitleHint |Qt.WindowMaximizeButtonHint  | Qt.CustomizeWindowHint)
            self.setWindowModality(Qt.ApplicationModal)
            self.error_label = QLabel("")
            self.error_label.setStyleSheet("color: #FF6B6B; font-size: 14px;")
            self.error_label.setAlignment(Qt.AlignCenter)
            self.timer = QTimer(self)
            self.timer.setInterval(60000)
            self.timer.timeout.connect(self.update_time)
            self.timer.start()
            self.window_mgmt_timer = QTimer(self)
            self.window_mgmt_timer.setInterval(60000)
            self.window_mgmt_timer.timeout.connect(self.minimize_all_windows)
            self.window_mgmt_timer.timeout.connect(self.bringToTop)
            self.window_mgmt_timer.start()

            self.load_templates()
            # Create QSpinBox for text size control
            self.text_size_spinbox = QSpinBox(self)
            self.text_size_spinbox.setRange(8, 40)  # Increased upper limit to 40
            self.text_size_spinbox.setValue(16)
            self.text_size_spinbox.valueChanged.connect(self.update_font_size)
            self.init_ui()
            self.load_config()  # Load size and text size from config
            self.update_style_sheet(self.text_size_spinbox.value())  # Apply initial text size

            # Check if we need to maximize based on text size
            if self.text_size_spinbox.value() > 23:
                self.showMaximized()

            # Minimize other windows but keep our window visible
            self.minimize_all_windows()

        except Exception as e:
            CLogger.MSwriteLog(f"Exception in BreakPopUp.__init__: {traceback.format_exc()}", log_userwise=True, userName=user)
            raise
        
    def update_style_sheet(self, font_size):
        style_sheet = f"""
            QWidget {{ background-color: #c5cfd9; color: #2c2f33; font-family: Arial; }}
            #MainFrame {{ border: 2px solid #aab4bf; border-radius: 8px; padding: 10px; background-color: #c5cfd9; }}
            #SubFrame {{ border: 2px solid #aab4bf; background-color: #c5cfd9; }}
            QLabel {{ font-size: {font_size}px; color: #2c2f33; }}
            QPushButton {{ background-color: #aab4bf; color: #2c2f33; border: 1px solid #8a929b; border-radius: 5px; padding: 5px; font-size: {font_size}px; }}
            QPushButton:hover {{ background-color: #bfc7d1; }}
            QComboBox, QLineEdit, QListWidget, QSpinBox {{ border: 1px solid #aab4bf; border-radius: 5px; padding: 3px; background-color: #dde2e8; color: #2c2f33; font-size: {font_size}px; }}
            QComboBox::drop-down {{ width: 20px; border-left: 1px solid #8a929b; background-color: #dde2e8; }}
            QComboBox::down-arrow {{ image: url(caret-down.png); width: 10px; height: 10px; background-color: #aab4bf; }}
            QListWidget::item:selected {{ background-color: #8a929b; color: #ffffff; }}
            QWidget:focus {{ border: 2px solid #495057; }}
        """
        self.setStyleSheet(style_sheet)

    def update_font_size(self, new_size):
        self.update_style_sheet(new_size)
        # Automatically maximize window if text size is greater than 23
        if new_size > 23:
            self.showMaximized()
        # For text size 23 or less, don't change window state - let user control it
        self.save_config()


    def load_templates(self):
        try:
            file_path = f"templates/{self.current_login_name}_template.json"
            dnd_file_path = f"templates/{self.current_login_name}_dnd_template.json"
            try:
                with open(file_path, "r") as f:
                    self.meeting_templates = json.load(f)
                for template in self.meeting_templates.values():
                    if "favorite" not in template:
                        template["favorite"] = False
            except FileNotFoundError:
                pass
            except json.JSONDecodeError:
                print("Error decoding JSON from template file; file may be corrupted.")
            try:
                with open(dnd_file_path, "r") as f:
                    self.dnd_templates = json.load(f)
                for template in self.dnd_templates.values():
                    if "favorite" not in template:
                        template["favorite"] = False
            except FileNotFoundError:
                pass
            except json.JSONDecodeError:
                print("Error decoding JSON from template file; file may be corrupted.")
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in load_templates: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def save_template_from_input(self, input_frame, template_type):
        try:
            if template_type == 'meeting':
                template_name = input_frame.template_name_input.text().strip()
                if not template_name or template_name == "Select a template":
                    QMessageBox.warning(self, "Error", "Please enter a valid template name.")
                    return
                try:
                    template_data = {
                        "customer": input_frame.customer_combo.currentText(),
                        "project": input_frame.project_combo.currentText(),
                        "with": [item.text().lstrip("☆★").strip() for item in input_frame.with_list.selectedItems()],
                        "favorite": False
                    }
                    self.meeting_templates[template_name] = template_data
                    os.makedirs("templates", exist_ok=True)
                    file_path = f"templates/{self.current_login_name}_template.json"
                    templates_to_update = self.meeting_templates
                    update_list = self.meeting_inputs
                    QMessageBox.information(self, "Success", f"Template '{template_name}' is saved successfully!")
                except Exception as e:
                    error_msg = f"Failed to save template: {str(e)}"
                    CLogger.MSwriteLog(f"Exception in save_template_from_input inner block: {traceback.format_exc()}", log_userwise=True, userName=self.user)
                    QMessageBox.critical(self, "Error", error_msg)
            elif template_type == 'dnd':
                template_name = input_frame.template_name_input.text().strip()
                if not template_name or template_name == "Select a template":
                    QMessageBox.warning(self, "Error", "Please enter a valid template name.")
                    return
                try:
                    template_data = {
                        "customer": input_frame.customer_combo.currentText(),
                        "project": input_frame.project_combo.currentText(),
                        "with": [item.text().lstrip("☆★").strip() for item in input_frame.with_list.selectedItems()],
                        "topic": input_frame.topic_input.text(),
                        "favorite": False
                    }
                    self.dnd_templates[template_name] = template_data
                    os.makedirs("templates", exist_ok=True)
                    file_path = f"templates/{self.current_login_name}_dnd_template.json"
                    templates_to_update = self.dnd_templates
                    update_list = self.dnd_inputs
                    QMessageBox.information(self, "Success", f"Template '{template_name}' is saved successfully!")
                except Exception as e:
                    error_msg = f"Failed to save template: {str(e)}"
                    CLogger.MSwriteLog(f"Exception in save_template_from_input inner block: {traceback.format_exc()}", log_userwise=True, userName=self.user)
                    QMessageBox.critical(self, "Error", error_msg)
            else:
                raise ValueError("Invalid template type")
            with open(file_path, "w") as f:
                json.dump(templates_to_update, f, indent=4)
            input_frame.update_templates(templates_to_update)
            for input_widget in update_list:
                input_widget.update_templates(templates_to_update)
            input_frame.template_name_input.clear()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in save_template_from_input: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def delete_template(self, input_frame, template_type):
        try:
            if template_type == 'meeting':
                selected_item = input_frame.meeting_template_list.currentItem()
                if not selected_item:
                    QMessageBox.warning(self, "Error", "Please select a template to delete.")
                    return
                template_name = selected_item.data(Qt.UserRole)
                if template_name not in self.meeting_templates:
                    QMessageBox.warning(self, "Error", f"Template '{template_name}' not found.")
                    return
                del self.meeting_templates[template_name]
                file_path = f"templates/{self.current_login_name}_template.json"
                templates_to_update = self.meeting_templates
                update_list = self.meeting_inputs
            elif template_type == 'dnd':
                selected_item = input_frame.dnd_template_list.currentItem()
                if not selected_item:
                    QMessageBox.warning(self, "Error", "Please select a template to delete.")
                    return
                template_name = selected_item.data(Qt.UserRole)
                if template_name not in self.dnd_templates:
                    QMessageBox.warning(self, "Error", f"Template '{template_name}' not found.")
                    return
                del self.dnd_templates[template_name]
                file_path = f"templates/{self.current_login_name}_dnd_template.json"
                templates_to_update = self.dnd_templates
                update_list = self.dnd_inputs
            else:
                raise ValueError("Invalid template type")
            with open(file_path, "w") as f:
                json.dump(templates_to_update, f, indent=4)
            input_frame.update_templates(templates_to_update)
            for input_widget in update_list:
                input_widget.update_templates(templates_to_update)
            QMessageBox.information(self, "Success", f"{template_type.capitalize()} template '{template_name}' has been deleted.")
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in delete_template: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def init_ui(self):
        try:
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(10, 10, 10, 10)
            main_layout.setSpacing(10)

            # Top layout for time and text size
            time_layout = QHBoxLayout()
            
            # Time container to keep "Time :" and value together
            time_container = QHBoxLayout()
            self.time_label = QLabel("Time :")
            self.time_value = QLabel(f"{self.minutes} min")
            self.time_label.setStyleSheet("color: #3e634c;  font-weight: bold;")
            self.time_value.setStyleSheet("color: #3e634c; font-weight: bold;")
            time_container.addWidget(self.time_label)
            time_container.addWidget(self.time_value)
            time_container.setSpacing(5)

            # Text size container
            text_size_container = QHBoxLayout()
            self.text_size_label = QLabel("Text size:")
            self.text_size_label.setStyleSheet("font-weight: bold;")
            text_size_container.addWidget(self.text_size_label)
            text_size_container.addWidget(self.text_size_spinbox)
            text_size_container.setSpacing(5)
            
            # Assemble time_layout with stretches for positioning
            time_layout.addStretch()  # Left stretch to center time
            time_layout.addLayout(time_container)  # Centered time display
            time_layout.addStretch()  # Right stretch to push text size to the right
            time_layout.addLayout(text_size_container)  # Text size on the right

            main_layout.addLayout(time_layout)

            self.scroll_area = QScrollArea()
            self.scroll_area.setWidgetResizable(True)
            self.entries_widget = QWidget()
            self.entries_layout = QVBoxLayout(self.entries_widget)
            self.scroll_area.setWidget(self.entries_widget)
            main_layout.addWidget(self.scroll_area)

            self.break_entries = []  # Initialize break_entries

            # Add the appropriate entry based on the type
            if self.is_half_day_leave:
                self.add_break_entry(initial_time="255", is_half_day_leave=True)
            else:
                self.add_break_entry(initial_time=str(self.minutes), is_dnd_fixed=self.after_dnd)

            btn_layout = QHBoxLayout()
            self.add_btn = QPushButton("Add")
            self.remove_btn = QPushButton("Remove")
            self.clear_btn = QPushButton("Clear")
            self.submit_btn = QPushButton("Submit")
            self.add_btn.clicked.connect(self.on_add_clicked)
            self.remove_btn.clicked.connect(self.on_remove_clicked)
            self.clear_btn.clicked.connect(self.on_clear_clicked)
            self.submit_btn.clicked.connect(self.on_submit_clicked)
            for btn in [self.add_btn, self.remove_btn, self.clear_btn, self.submit_btn]:
                btn.setFixedWidth(120)
                btn_layout.addWidget(btn)
            btn_layout.addStretch()
            main_layout.addLayout(btn_layout)
            main_layout.addWidget(self.error_label)
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in init_ui: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def load_config(self):
        """Load the dialog size and text size from a user-specific configuration file."""
        config_path = f"files/{self.user}TextSize.json"
        try:
            if os.path.exists(config_path):
                with open(config_path, "r") as f:
                    config = json.load(f)
                    text_size = config.get("text_size", 16)
                    self.text_size_spinbox.setValue(text_size)  # Set spinbox value from config

                    # If text size > 23, force maximize the window
                    if text_size > 23:
                        self.showMaximized()
                    # For text size 23 or less, don't change window state
            else:
                self.text_size_spinbox.setValue(16)  # Default text size
        except (json.JSONDecodeError, Exception) as e:
            CLogger.MSwriteLog(f"Error loading config: {e}", log_userwise=True, userName=self.user)

    def save_config(self):
        """Save the current dialog size and text size to a user-specific configuration file."""
        config_path =f"files/{self.user}TextSize.json"
        try:
            os.makedirs("files", exist_ok=True)
            config = {
                "text_size": self.text_size_spinbox.value()
            }
            with open(config_path, "w") as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            CLogger.MSwriteLog(f"Error saving config: {e}", log_userwise=True, userName=self.user)


    def add_break_entry(self, initial_time="0", is_dnd_fixed=False, is_half_day_leave=False):
        try:
            entry = BreakEntry(self.user, self.dictUsersData, self.dictCustomerData, self.meeting_templates,
                              self.allowed_customers, self.dnd_templates, self.favorite_users,
                              initial_time, is_dnd_fixed, is_half_day_leave)
            color_index = len(self.break_entries) % len(self.color_list)
            color = self.color_list[color_index]
            entry.setStyleSheet(f"QFrame {{ background-color: {color}; }}")
            self.entries_layout.addWidget(entry)
            self.break_entries.append(entry)
            if hasattr(entry, 'meeting_input'):
                self.meeting_inputs.append(entry.meeting_input)
            if hasattr(entry, 'dnd_input'):
                self.dnd_inputs.append(entry.dnd_input)
            if hasattr(entry, 'half_day_leave_input'):
                self.half_day_leave_inputs.append(entry.half_day_leave_input)
            self.update_time_inputs()
            self.connect_time_input_signals()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in add_break_entry: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def update_time_inputs(self):
        try:
            # Check if we have a Half Day Leave entry
            has_half_day_leave = any(entry.break_type_combo.currentText() == "Half Day Leave" for entry in self.break_entries)

            # For Half Day Leave, don't auto-set the time
            if has_half_day_leave and len(self.break_entries) == 1:
                # For Half Day Leave, set time to 255 and make it read-only
                self.break_entries[0].time_input.setText("255")
                self.break_entries[0].set_time_input_readonly(True)
            elif len(self.break_entries) == 1:
                # For other break types with a single entry, set the time to minutes
                self.break_entries[0].time_input.setText(str(self.minutes))
                self.break_entries[0].set_time_input_readonly(True)
            else:
                total_time = sum(
                    int(entry.time_input.text()) 
                    for entry in self.break_entries[:-1] 
                    if entry.time_input.text().isdigit()
                )
                remaining_time = self.minutes - total_time
                if remaining_time < 0:
                    remaining_time = 0

                # Don't auto-set time for Half Day Leave entries
                if self.break_entries[-1].break_type_combo.currentText() == "Half Day Leave":
                    self.break_entries[-1].time_input.setText("255")
                    self.break_entries[-1].set_time_input_readonly(True)
                else:
                    self.break_entries[-1].time_input.setText(str(remaining_time))
                    self.break_entries[-1].set_time_input_readonly(True)

                # Make all entries except the last one editable, unless it's Half Day Leave
                for entry in self.break_entries[:-1]:
                    if entry.break_type_combo.currentText() == "Half Day Leave":
                        entry.time_input.setText("255")
                        entry.set_time_input_readonly(True)
                    else:
                        entry.set_time_input_readonly(False)

                # Make the last entry editable if it's Half Day Leave
                if self.break_entries[-1].break_type_combo.currentText() == "Half Day Leave":
                    self.break_entries[-1].set_time_input_readonly(False)
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in update_time_inputs: {traceback.format_exc()}", log_userwise=True, userName=self.user)

    def connect_time_input_signals(self):
        try:
            for entry in self.break_entries[:-1]:
                if entry.time_input.receivers(entry.time_input.textChanged) > 0:
                    entry.time_input.textChanged.disconnect()
                entry.time_input.textChanged.connect(self.update_time_inputs)
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in connect_time_input_signals: {traceback.format_exc()}", log_userwise=True, userName=self.user)

    def update_time(self):
        try:
            self.minutes += 1
            self.time_value.setText(f"{self.minutes} min")
            self.update_time_inputs()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in update_time: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def minimize_all_windows(self):
        try:
            CLogger.MSwriteLog("Minimizing all windows.", "info", log_userwise=True)

            # Store current window state
            was_maximized = self.isMaximized()

            # Minimize all windows
            win32api.keybd_event(win32con.VK_LWIN, 0, 0, 0)
            win32api.keybd_event(0x4D, 0, 0, 0)
            win32api.keybd_event(0x4D, 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_LWIN, 0, win32con.KEYEVENTF_KEYUP, 0)
            self.bringToTop()

            # Restore maximized state if needed
            if was_maximized:
                self.showMaximized()
        except Exception as e:
            CLogger.MSwriteLog(f"Error minimizing windows: {e}", log_userwise=True)
            raise

    def restore_all_windows(self):
        try:
            CLogger.MSwriteLog("Restoring all windows", "info")
            win32api.keybd_event(win32con.VK_LWIN, 0, 0, 0)
            win32api.keybd_event(win32con.VK_SHIFT, 0, 0, 0)
            win32api.keybd_event(0x4D, 0, 0, 0)
            win32api.keybd_event(0x4D, 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_SHIFT, 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_LWIN, 0, win32con.KEYEVENTF_KEYUP, 0)
        except Exception as e:
            CLogger.MSwriteLog(f"Error restoring windows: {e}")
            raise

    def bringToTop(self):
        try:
            # Store current window state
            was_maximized = self.isMaximized()

            self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
            self.show()
            self.raise_()
            self.activateWindow()

            # Only center if not maximized
            if not was_maximized:
                screen = QApplication.primaryScreen()
                screen_geometry = screen.availableGeometry()
                window_geometry = self.frameGeometry()
                center_point = screen_geometry.center()
                window_geometry.moveCenter(center_point)
                self.move(window_geometry.topLeft())

            # Restore maximized state if needed
            if was_maximized:
                self.showMaximized()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in bringToTop: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def removeAlwaysOnTop(self):
        try:
            # Store current window state
            was_maximized = self.isMaximized()

            flags = self.windowFlags() & ~Qt.WindowStaysOnTopHint
            self.setWindowFlags(flags)
            self.show()

            # Restore maximized state if needed
            if was_maximized:
                self.showMaximized()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in removeAlwaysOnTop: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def on_add_clicked(self):
        try:
            # Store current window state
            was_maximized = self.isMaximized()

            self.add_break_entry(initial_time="0")

            # Reset time inputs but preserve Half Day Leave entries
            for entry in self.break_entries:
                if entry.break_type_combo.currentText() != "Half Day Leave":
                    entry.time_input.setText("0")

            # Update time inputs to properly distribute time
            self.update_time_inputs()


            # Only adjust size if not maximized
            if not was_maximized:
                self.adjustSize()
            QApplication.processEvents()

            # Restore maximized state if needed
            if was_maximized:
                self.showMaximized()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in on_add_clicked: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def on_remove_clicked(self):
        try:
            if len(self.break_entries) > 1:
                # Store current window state
                was_maximized = self.isMaximized()

                entry = self.break_entries.pop()
                self.entries_layout.removeWidget(entry)
                if hasattr(entry, 'meeting_input'):
                    self.meeting_inputs.remove(entry.meeting_input)
                if hasattr(entry, 'dnd_input'):
                    self.dnd_inputs.remove(entry.dnd_input)
                if hasattr(entry, 'half_day_leave_input'):
                    self.half_day_leave_inputs.remove(entry.half_day_leave_input)
                entry.deleteLater()
                self.connect_time_input_signals()
                if len(self.break_entries) == 1:
                    # If it's a Half Day Leave entry, don't auto-set the time
                    if not (self.is_half_day_leave and self.break_entries[0].break_type_combo.currentText() == "Half Day Leave"):
                        self.break_entries[0].time_input.setText(str(self.minutes))

                # Only adjust size if not maximized
                if not was_maximized:
                    self.adjustSize()
                QApplication.processEvents()

                # Restore maximized state if needed
                if was_maximized:
                    self.showMaximized()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in on_remove_clicked: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def on_clear_clicked(self):
        try:
            # Store current window state
            was_maximized = self.isMaximized()

            for entry in self.break_entries:
                entry.clear()
            self.error_label.setText("")

            # Ensure window state is preserved
            if was_maximized:
                self.showMaximized()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in on_clear_clicked: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def update_all_with_lists(self):
        try:
            for entry in self.break_entries:
                if hasattr(entry, 'meeting_input'):
                    entry.meeting_input.favorite_users = self.favorite_users
                    entry.meeting_input.populate_with_list()
                if hasattr(entry, 'dnd_input'):
                    entry.dnd_input.favorite_users = self.favorite_users
                    entry.dnd_input.populate_with_list()
                if hasattr(entry, 'half_day_leave_input'):
                    entry.half_day_leave_input.favorite_users = self.favorite_users
                    entry.half_day_leave_input.populate_with_list()
        except Exception:
            traceback.print_exc()

    def update_all_meeting_inputs(self):
        try:
            for entry in self.break_entries:
                if hasattr(entry, 'meeting_input'):
                    entry.meeting_input.update_templates(self.meeting_templates)
        except Exception:
            traceback.print_exc()

    def update_all_dnd_inputs(self):
        try:
            for entry in self.break_entries:
                if hasattr(entry, 'dnd_input'):
                    entry.dnd_input.update_templates(self.dnd_templates)
        except Exception:
            traceback.print_exc()

    def on_submit_clicked(self):
        try:
            # Store current window state
            was_maximized = self.isMaximized()

            CLogger.MSwriteLog("Submit button clicked", "info", log_userwise=True, userName=self.user)
            self.error_label.setText("")
            # Validate time inputs
            has_error = False
            for entry in self.break_entries:
                entry.time_input.setStyleSheet("border: 1px solid #CED4DA;")
                try:
                    time_val = int(entry.time_input.text())
                    # Skip time validation for Half Day Leave entries
                    if entry.break_type_combo.currentText() != "Half Day Leave" and time_val <= 0:
                        entry.time_input.setStyleSheet("border: 2px solid #FF6B6B;")
                        has_error = True
                    # Ensure Half Day Leave entries have time set to 255
                    if entry.break_type_combo.currentText() == "Half Day Leave" and time_val != 255:
                        entry.time_input.setStyleSheet("border: 2px solid #FF6B6B;")
                        has_error = True
                except ValueError:
                    entry.time_input.setStyleSheet("border: 2px solid #FF6B6B;")
                    has_error = True
            if has_error:
                self.error_label.setText("All estimated times must be positive integers greater than zero.")
                # Ensure window state is preserved on error
                if was_maximized:
                    self.showMaximized()
                return
            # Initialize the output dictionary
            overall_log_data = {
                "BreakType": [],
                "strTimeInMin": [],
                "Meeting_with": [],
                "Meeting_Project": [],
                "Meeting_Customer": [],
                "Meeting_Topic": [],
                "DND_with": [],
                "DND_Project": [],
                "DND_Customer": [],
                "DND_Topic": [],
                "Interview_CandidateName": [],
                "Interview_Position": [],
                "Interview_Team": [],
                "Interview_Customer": [],
                "HalfDayLeave_ApprovedBy": [],
                "HalfDayLeave_Notes": [],
            }
            # Aggregate data from all entries
            for entry in self.break_entries:
                valid, error_message = entry.validate()
                if not valid:
                    self.error_label.setText(error_message)
                    # Ensure window state is preserved on error
                    if was_maximized:
                        self.showMaximized()
                    return
                data = entry.get_data()
                overall_log_data["BreakType"].append(data["BreakType"])
                overall_log_data["strTimeInMin"].append(data["strTimeInMin"])
                overall_log_data["Meeting_with"].append(data["Meeting_with"])
                overall_log_data["Meeting_Project"].append(data["Meeting_Project"])
                overall_log_data["Meeting_Customer"].append(data["Meeting_Customer"])
                overall_log_data["Meeting_Topic"].append(data["Meeting_Topic"])
                overall_log_data["DND_with"].append(data["DND_with"])
                overall_log_data["DND_Project"].append(data["DND_Project"])
                overall_log_data["DND_Customer"].append(data["DND_Customer"])
                overall_log_data["DND_Topic"].append(data["DND_Topic"])
                overall_log_data["Interview_CandidateName"].append(data["Interview_CandidateName"])
                overall_log_data["Interview_Position"].append(data["Interview_Position"])
                overall_log_data["Interview_Team"].append(data["Interview_Team"])
                overall_log_data["Interview_Customer"].append(data["Interview_Customer"])
                overall_log_data["HalfDayLeave_ApprovedBy"].append(data.get("HalfDayLeave_ApprovedBy", []))
                overall_log_data["HalfDayLeave_Notes"].append(data.get("HalfDayLeave_Notes", ""))
            # Validate total time and DND topic for fixed DND entries
            total_time = sum(int(t) for t in overall_log_data["strTimeInMin"])
            has_fixed_dnd = any(entry.is_dnd_fixed for entry in self.break_entries)
            has_half_day_leave = any(entry.break_type_combo.currentText() == "Half Day Leave" for entry in self.break_entries)

            # Skip time matching validation for Half Day Leave
            if has_half_day_leave and len(self.break_entries) == 1:
                # Skip time validation for Half Day Leave
                pass
            elif len(self.break_entries) > 1:
                if abs(total_time - self.minutes) > 1:
                    self.error_label.setText("Estimated time must match the actual time.")
                    for entry in self.break_entries:
                        entry.time_input.setStyleSheet("border: 2px solid #FF6B6B;")
                    # Ensure window state is preserved on error
                    if was_maximized:
                        self.showMaximized()
                    return
            elif has_fixed_dnd:
                # Check if any fixed DND entry has an empty topic
                for i, entry in enumerate(self.break_entries):
                    if entry.is_dnd_fixed and entry.break_type_combo.currentText() == "DND":
                        if not overall_log_data["DND_Topic"][i].strip():
                            self.error_label.setText("DND topic must not be empty for fixed DND entries.")
                            entry.dnd_input.highlight_field(entry.dnd_input.topic_input)
                            # Ensure window state is preserved on error
                            if was_maximized:
                                self.showMaximized()
                            return
                # Ensure total time matches exactly for single fixed DND entry
                if total_time != self.minutes:
                    self.error_label.setText("Estimated time must match the actual time.")
                    for entry in self.break_entries:
                        entry.time_input.setStyleSheet("border: 2px solid #FF6B6B;")
                    # Ensure window state is preserved on error
                    if was_maximized:
                        self.showMaximized()
                    return
            else:
                if total_time != self.minutes:
                    self.error_label.setText("Estimated time must match the actual time.")
                    for entry in self.break_entries:
                        entry.time_input.setStyleSheet("border: 2px solid #FF6B6B;")
                    # Ensure window state is preserved on error
                    if was_maximized:
                        self.showMaximized()
                    return
            # Set the result and close the dialog
            self.result = overall_log_data
            self.accept()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in on_submit_clicked: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

    def keyPressEvent(self, event):
        try:
            if event.key() == Qt.Key_Escape:
                QMessageBox.warning(self, "Action Blocked", "Please submit your data to close the dialog.")
                event.ignore()  # Explicitly ignore the Esc key event
                return
            if event.text().isalpha():
                letter = event.text().lower()
                # This section remains unchanged as it references instance variables not defined in this context
            if event.key() in (Qt.Key_Return, Qt.Key_Enter):
                # This section remains unchanged as it references instance variables not defined in this context
                pass
            elif event.key() == Qt.Key_Down:
                # This section remains unchanged as it references instance variables not defined in this context
                pass
            elif event.key() == Qt.Key_Up:
                # This section remains unchanged as it references instance variables not defined in this context
                pass
            super().keyPressEvent(event)
        except Exception:
            traceback.print_exc()


    def changeEvent(self, event):
        """Handle window state changes."""
        try:
            super().changeEvent(event)
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in changeEvent: {traceback.format_exc()}", log_userwise=True, userName=self.user)

    def closeEvent(self, event):
        try:
            if not self.result:
                QMessageBox.warning(self, "Action Blocked", "Please submit your data before closing.")
                event.ignore()
            else:
                event.accept()
        except Exception as e:
            CLogger.MSwriteLog(f"Exception in closeEvent: {traceback.format_exc()}", log_userwise=True, userName=self.user)
            raise

def create_break_popup(minutes, user, dicMonConfig, dictUsersData, dictGeneralAccount, dictCustomerData, after_dnd=False, is_half_day_leave=False):
    try:
        CLogger.MSwriteLog("create_break_popup function started.", "info", log_userwise=True, userName=user)
        if not QApplication.instance():
            raise RuntimeError("QApplication must be initialized on the main thread before calling this function.")
        popup = BreakPopUp(minutes, user, dicMonConfig, dictUsersData, dictGeneralAccount, dictCustomerData,
                          after_dnd=after_dnd, is_half_day_leave=is_half_day_leave)
        if popup.exec_() == QDialog.Accepted:
            return popup.result
        else:
            return None
    except Exception as e:
        CLogger.MSwriteLog(f"Exception in create_break_popup: {traceback.format_exc()}", log_userwise=True, userName=user)
        raise

if __name__ == "__main__":
    print("Entering __main__")
    try:
        with open("files/Popup_Gui_Config.json", "r") as f:
            dict_config = json.load(f)
        with open(dict_config["UserData_filepath"], "r") as f:
            dict_UserData = json.load(f)
        with open(dict_config["CustomerData_filepath"], "r") as f:
            dictCustomerData = json.load(f)
        with open(dict_config["GeneralAccount_filepath"], "r") as f:
            dict_GeneralAccount = json.load(f)
    except Exception as e:
        print("Error loading configuration:", e)
        dict_config = {"Break_types": ["Break", "Meeting", "Interview", "System Down", "Working On Other System"], "Team_Types": []}
        dict_UserData = {}
        dictCustomerData = {}
        dict_GeneralAccount = {}

    app = QApplication(sys.argv)
    result = create_break_popup(minutes="10", user="d0013", dicMonConfig=dict_config, dictUsersData=dict_UserData,
                                dictGeneralAccount=dict_GeneralAccount, dictCustomerData=dictCustomerData)
    print("Result from popup in __main__:", result)
    sys.exit(app.exec_())