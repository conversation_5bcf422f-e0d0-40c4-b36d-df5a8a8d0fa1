import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from monHelper import <PERSON><PERSON><PERSON><PERSON>

def send_half_day_leave_email(employee_full_name, manager_emails, leave_duration_minutes, approvers, notes):
    # Convert leave duration from minutes to a readable string
    hours, minutes = divmod(leave_duration_minutes, 60)
    if hours > 0 and minutes > 0:
        duration_str = f"{hours} hours {minutes} minutes"
    elif hours > 0:
        duration_str = f"{hours} hours"
    else:
        duration_str = f"{minutes} minutes"
        
    # Read the JSON file
    try:
        with open('files\\config.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError:
        error_msg = "Error: config.json not found."
        CLogger.MSwriteLog(error_msg, "error")
        raise FileNotFoundError(error_msg)
    except json.JSONDecodeError:
        error_msg = "Error: Invalid JSON in config.json."
        CLogger.MSwriteLog(error_msg, "error")
        raise json.JSONDecodeError(error_msg)
        
    # Extract required data with validation
    username = config.get('username')
    password = config.get('password')
    cc_list = config.get('cc', [])
    bcc_list = config.get('bcc', [])
    if not username or not password:
        error_msg = "Error: 'username' or 'password' missing in config.json."
        CLogger.MSwriteLog(error_msg, "error")
        raise ValueError(error_msg)
    if not isinstance(cc_list, list):
        error_msg = "Error: 'cc' must be a list in config.json."
        CLogger.MSwriteLog(error_msg, "error")
        raise ValueError(error_msg)
    if not isinstance(bcc_list, list):
        error_msg = "Error: 'bcc' must be a list in config.json."
        CLogger.MSwriteLog(error_msg, "error")
        raise ValueError(error_msg)
    
    # Log if BCC is empty or not present
    if not bcc_list:
        CLogger.MSwriteLog("No BCC recipients specified in config.json.", "info")
    
    # Ensure approvers is a list
    if not isinstance(approvers, list):
        approvers = [approvers] if approvers else []
    
    # Construct the email body
    body = f"""
        Dear Manager,

        This is to inform you that {employee_full_name} has submitted a half-day leave request.

        Details:
        - Employee: {employee_full_name}
        - Leave Duration: {duration_str}
        - Approved By: {', '.join(approvers) if approvers else 'N/A'}
        - Notes: {notes if notes else 'N/A'}

        Please let us know if you have any questions or concerns.

        Best regards,
        Developer Team
        """
    
    # Email configuration
    sender_email = username
    subject = f"Half Day Leave for Employee {employee_full_name}"
    
    # Create the email message
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = ", ".join(manager_emails)
    msg['Cc'] = ", ".join(cc_list) if cc_list else ""
    msg['Subject'] = subject
    msg.attach(MIMEText(body, 'plain'))
    
    # SMTP settings
    smtp_server = "smtppro.zoho.in"
    smtp_port = 465
    smtp_username = username
    smtp_password = password
    
    # Send the email
    try:
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
            server.login(smtp_username, smtp_password)
            server.sendmail(sender_email, manager_emails + cc_list + bcc_list, msg.as_string())
        success_msg = f"Email sent successfully to {manager_emails} with CC {cc_list} and BCC {bcc_list}"
        print(success_msg)
        CLogger.MSwriteLog(success_msg, "info")
    except smtplib.SMTPAuthenticationError as auth_err:
        error_msg = f"Authentication failed: {auth_err}"
        CLogger.MSwriteLog(error_msg, "error")
        raise
    except smtplib.SMTPException as smtp_err:
        error_msg = f"SMTP error: {smtp_err}"
        CLogger.MSwriteLog(error_msg, "error")
        raise
    except Exception as e:
        error_msg = f"Failed to send email: {e}"
        CLogger.MSwriteLog(error_msg, "error")
        raise

def process_break_details(user, break_details, dict_users_data):
    # Process break details and send emails for half-day leave entries
    if break_details is not None:
        for i, break_type in enumerate(break_details["BreakType"]):
            if break_type == "Half Day Leave":
                # Extract required information
                employee_full_name = dict_users_data.get(user, {}).get("fullName", user)
                managers = dict_users_data.get(user, {}).get("Manager", [])
                manager_emails = [f"{manager}@riveredgeanalytics.com" for manager in managers]
                if not manager_emails:
                    error_msg = f"No managers assigned to {user}. Email not sent."
                    CLogger.MSwriteLog(error_msg, "warning")
                    return
                leave_duration_minutes = int(break_details["strTimeInMin"][i])
                approvers = break_details["HalfDayLeave_ApprovedBy"][i]
                notes = break_details["HalfDayLeave_Notes"][i]
                
                # Send the email
                send_half_day_leave_email(
                    employee_full_name=employee_full_name,
                    manager_emails=manager_emails,
                    leave_duration_minutes=leave_duration_minutes,
                    approvers=approvers,
                    notes=notes
                )