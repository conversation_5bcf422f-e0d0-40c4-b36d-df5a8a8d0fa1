{"anirudh": {"Manager": [], "Team": ["harshil", "<PERSON><PERSON><PERSON>", "manthan", "murari", "nidhi"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON><PERSON>", "IKIO"], "DataStore_path": "C:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": false, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": true, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}, "smeeta": {"Manager": [], "Team": ["nikhil", "jignesh", "manthan", "murari", "nidhi", "dell"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "IBMSB - TAX", "<PERSON>", "ABHA_PC"], "DataStore_path": "C:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": false, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": true, "fullName": "<PERSON><PERSON><PERSON>"}, "murari": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>", "smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "Anand Tea", "Bengal Tea"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "manthan": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>", "smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "harshil": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>"], "Team": ["mitul", "dhru<PERSON>", "mohit", "mansi", "id0005", "Admin", "rutvik", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "user", "temp1", "temp2", "jays<PERSON>ee", "d005", "free1", "free2", "free3", "id0014", "id0015", "id0016", "id0020", "id0021", "id0025"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON><PERSON>"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "devanshi": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 2, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": true, "fullName": "<PERSON><PERSON><PERSON>"}, "mitul": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "dhruvin": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON><PERSON>"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}, "temp1": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "temp2": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Sanket Gohil"}, "rutvik": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "pavan": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "mansi": {"Manager": ["<PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0005": {"Manager": ["<PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "nikhil": {"Manager": ["smeeta"], "Team": ["id0007", "gaurav", "fenil", "punesh", "river131", "river129", "river130", "jatin", "divy<PERSON><PERSON>", "temp1", "temp3", "temp4", "guest1", "guest2", "guest3", "guest4", "int01", "int02", "int03", "int04", "int05", "int06", "int07", "int08", "int09", "int10", "verifier", "id0022", "id0023", "id0024", "id0026", "id0027", "id0028", "id0029", "id0030", "id0031", "id0032", "id0033", "id0034", "id0035", "id0040", "REAL_L04", "REAL_L03"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "WorkWell Medical Group", "ABHA_PC", "<PERSON><PERSON>", "Akeso", "<PERSON><PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "IBMSB - ACCOUNTS"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0040": {"Manager": ["nikhil"], "Team": ["mansi.pithadia"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "mansi.pithadia": {"Manager": ["nikhil", "divy<PERSON><PERSON>", "ni<PERSON>h"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "<PERSON>", "<PERSON>", "RV", "<PERSON><PERSON>", "DHOA", "IBMSB - ACCOUNTS", "Next Practise Partners"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0007": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON>", "BAOMG", "<PERSON><PERSON><PERSON>", "Akeso"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON>"}, "gaurav": {"Manager": ["nikhil", "raj"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "BAOMG", "Akeso", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}, "fenil": {"Manager": ["nikhil", "divy<PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "<PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "punesh": {"Manager": ["nikhil", "ni<PERSON>h"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "<PERSON>", "<PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "IBMSB - ACCOUNTS"], "DataStore_path": "C:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "river131": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IBMSB - TAX", "IKIO", "ABHA_PC", "<PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "IBMSB - ACCOUNTS", "Fresh Food"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "divyansh": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "WorkWell Medical Group", "<PERSON>", "Akeso", "<PERSON>", "<PERSON><PERSON>", "RV", "Luxeen America", "DHOA", "<PERSON><PERSON>", "Fresh Food", "Next Practise Partners", "<PERSON><PERSON><PERSON>"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}, "river129": {"Manager": ["nikhil", "divy<PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "<PERSON><PERSON>", "Fresh Food"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "river130": {"Manager": ["nikhil", "divy<PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "<PERSON>", "<PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "jatin": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "<PERSON>", "Fresh Food", "IBMSB - ACCOUNTS"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "JAYSHREE": {"Manager": ["<PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "F:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "d002": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 2, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["<PERSON><PERSON><PERSON>", "<PERSON>", "IKIO", "Real"], "DataStore_path": "D:\\mon\\TestData", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": true, "fullName": "<PERSON><PERSON><PERSON>"}, "d005": {"Manager": ["<PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0008": {"Manager": ["smeeta", "ar<PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IBMSB - TAX", "IBMSB - ACCOUNTS", "<PERSON>", "Fresh Food"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0009": {"Manager": ["smeeta", "ar<PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IBMSB - TAX", "IBMSB - ACCOUNTS"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0010": {"Manager": ["smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IBMSB - TAX"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0012": {"Manager": ["smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IBMSB - TAX"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0017": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>", "smeeta", "raj"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "<PERSON>"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "temp3": {"Manager": ["smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "<PERSON>"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0019": {"Manager": ["smeeta", "<PERSON><PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON> Gohil"}, "id0018": {"Manager": ["nikhil", "ni<PERSON>h"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "RV", "<PERSON><PERSON>", "<PERSON>"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>ad<PERSON>"}, "id0013": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON>", "<PERSON>"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "d006": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "d007": {"Manager": ["d001"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON>"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "d008": {"Manager": ["d001"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "d009": {"Manager": ["d001"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "d0010": {"Manager": ["harshil", "mitul"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "d0011": {"Manager": ["d001"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "d0013": {"Manager": ["d001"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "river": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON><PERSON>"], "DataStore_path": "C:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}, "sams2": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON><PERSON>", "IKIO", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "WorkWell Medical Group", "ABHA_PC", "<PERSON><PERSON>", "Akeso", "<PERSON><PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "BAOMG", "IBMSB - TAX", "Anand Tea", "Bengal Tea", "IBMSB - ACCOUNTS", "Fresh Food", "Next Practise Partners"], "DataStore_path": "C:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": false, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": true, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}, "accuvelocityai": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "planet": {"Manager": [], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": false, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "prashant": {"Manager": [], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": false, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "temp4": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "guest1": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "guest2": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "guest3": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "guest4": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int01": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int02": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int03": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int04": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int05": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int06": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int07": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int08": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int09": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "int10": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "verifier": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "free1": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "free2": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "free3": {"Manager": ["harshil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "d0014": {"Manager": ["<PERSON><PERSON><PERSON>", "acc<PERSON><PERSON><PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0015": {"Manager": ["nikhil", "divy<PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0016": {"Manager": ["<PERSON><PERSON><PERSON><PERSON>", "smeeta", "<PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0020": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON>", "<PERSON>"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0021": {"Manager": ["nikhil", "raj"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0022": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0023": {"Manager": ["nikhil", "ni<PERSON>h"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "IBMSB - ACCOUNTS", "<PERSON><PERSON>"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0024": {"Manager": ["nikhil", "ni<PERSON>h"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0025": {"Manager": ["nikhil", "divy<PERSON><PERSON>"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0026": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0027": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0028": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0029": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0030": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0031": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0032": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "Third Party"}, "id0033": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "id0034": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "user": {"Manager": ["smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "WorkWell Medical Group", "ABHA_PC", "<PERSON><PERSON>", "Akeso", "<PERSON><PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "IBMSB - ACCOUNTS"], "DataStore_path": "C:\\Mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "dell": {"Manager": ["smeeta"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "WorkWell Medical Group", "ABHA_PC", "<PERSON><PERSON>", "Akeso", "<PERSON><PERSON>", "RV", "<PERSON><PERSON>", "Luxeen America", "DHOA", "IBMSB - ACCOUNTS"], "DataStore_path": "C:\\Mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON>"}, "id0035": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real"], "DataStore_path": "J:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Devanshi": {"fullName": "<PERSON><PERSON><PERSON>", "Manager": ["<PERSON><PERSON><PERSON><PERSON>"], "List_Of_Customer": ["<PERSON><PERSON><PERSON>", "<PERSON>", "IKIO", "Real"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false}, "DEVANSHI": {"fullName": "<PERSON><PERSON><PERSON>", "Manager": ["<PERSON><PERSON><PERSON><PERSON>"], "List_Of_Customer": ["<PERSON><PERSON><PERSON>", "<PERSON>", "IKIO", "Real"], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "DataStore_path": "D:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false}, "real_l04": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "<PERSON><PERSON>", "<PERSON>"], "DataStore_path": "C:\\mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON>"}, "real_l03": {"Manager": ["nikhil"], "Team": [], "IBREAK_TIME_MIN": 10, "LOG_INTERVAL_SEC": 15, "IMAGE_LOG_MULTIPLE": 3, "INACTIVITY_THRESHOLD_MIN": 1, "List_Of_Customer": ["Real", "IKIO", "ABHA_PC", "WorkWell Medical Group", "<PERSON>", "Akeso", "<PERSON>", "<PERSON><PERSON>", "RV", "Luxeen America", "DHOA", "<PERSON><PERSON>", "Fresh Food", "Next Practise Partners", "<PERSON><PERSON><PERSON>"], "DataStore_path": "C:\\Mon\\Data", "MinSpaceInGB": 10, "bSendEmailNotification": true, "bCaptureScreenshot": true, "bEnableTransfer": true, "bEnableTouchScreenMonitoring": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>"}}