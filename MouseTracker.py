# this code can be used to track mouse activity every few seconds...very light weight
# <PERSON><PERSON><PERSON><PERSON>, 12th Sept, 2024
import threading
import time
from pynput import mouse

class MouseActivityTracker:
    def __init__(self):
        self.last_position = (0, 0)


    def getCurrentPosition(self):
        return mouse.Controller().position
        

    def compareCurrentVsLastPosition(self, threshold = 2):
        bMoved = False
        try:
            current_position = self.getCurrentPosition()
            dx = abs(current_position[0] - self.last_position[0])
            dy = abs(current_position[1] - self.last_position[1])
            bMoved = dx > threshold or dy > threshold
            self.set_last_position(current_position)  
            return bMoved
        except Exception as e:   
            return bMoved

    def set_last_position(self, position):
        self.last_position = position


if __name__ == "__main__":

    # Example usage:
    tracker = MouseActivityTracker()

    try:
        while True:
            time.sleep(1)
            if tracker.compareCurrentVsLastPosition():
                print("Mouse is active")
            else:
                print("Mouse is inactive")
            #print(f"Last position: {tracker.get_last_position()}")
    except KeyboardInterrupt:
        print('Stop')