import os
import logging
import socket
import pytz
from win32gui import GetWindowText, GetForegroundWindow, GetWindowRect
import win32process
import psutil
from cryptography.fernet import Fernet
from key import strKey
from PIL import Image
import time as ti
from typing import Optional, Tuple
import pandas as pd
import datetime
import json
import pyautogui
import shutil
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List
import traceback
import win32com.client
import psutil
import win32process
from desktopmagic.screengrab_win32 import (getScreenAsImage)
# import pytesseract
import re
from urllib.parse import urlparse
#import cv2
import numpy as np
# Set the path to the tesseract executable
# pytesseract.pytesseract.tesseract_cmd = r'files\Teseeract\tesseract.exe'

class CMonHelper:
    @staticmethod
    def MSGetComputerName() -> str:
        """
        Retrieves the current computer's name.

        Returns:
            str: The name of the computer.

        Raises:
            OSError: If there is an issue retrieving the computer name.
        """
        strComputerName = ""
        try:
            strComputerName = os.uname().nodename if hasattr(os, 'uname') else os.getenv('COMPUTERNAME')
            
            if not strComputerName:
                strComputerName = socket.gethostname()
        except:
            try:
                strComputerName = socket.gethostname()
            except:
                CLogger.MSwriteLog(traceback.format_exc())
                
        return strComputerName

    @staticmethod
    def MSGetLoggedUserName() -> str:
        """
        Retrieves the name of the currently logged-in user.

        Returns:
            str: The username of the logged-in user.

        Raises:
            OSError: If there is an issue retrieving the username.
        """
        strUserName = ""
        try:
            strUserName = os.getlogin()
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

        
        return strUserName

    @staticmethod
    def MSGetActiveWindowDetails(dictExclusionData):
        dictWindowDetails = {
            "WindowTitle": "",
            "ExeLocation": "",
            "WindowRect": None
        }
        try:
            # Try to capture the active window details, retry up to 3 times if WindowTitle is empty
            max_retries = 2
            retry_count = 0
            
            while retry_count <= max_retries:
                # Get the handle to the currently active window
                hwnd = GetForegroundWindow()
                strWindowTitle = GetWindowText(hwnd)
                # Get the window's rectangle (coordinates)
                try:
                    window_rect = GetWindowRect(hwnd)
                    dictWindowDetails["WindowRect"] = window_rect
                except:
                    pass
                _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                process = psutil.Process(process_id)
                # Get the path to the executable
                strExeLocation = process.exe()
                
                # Log details for debugging
                # CLogger.MSwriteLog(
                #     f"Attempt {retry_count}: Title='{strWindowTitle}', Exe='{strExeLocation}'",
                #     "debug",
                #     log_userwise=True,
                #     userName="unknown"
                # )
                
                 # If WindowTitle is not empty, and current window is not related to explorer proceed
                if strWindowTitle and not CMonHelper.MSBShouldExclude(
                    strWindowTitle.lower(), strExeLocation.lower(), dictExclusionData
                ):
                    dictWindowDetails["WindowTitle"] = strWindowTitle
                    dictWindowDetails["ExeLocation"] = strExeLocation
                    break
                # If WindowTitle is empty, wait for 10 seconds and retry
                retry_count += 1
                ti.sleep(5)

            # Check if the active window is Windows Explorer then try to get more information
            if "explorer.exe" in strExeLocation.lower():
                explorer_details = CMonHelper.MSGetExplorerDetails(hwnd)
                dictWindowDetails["WindowTitle"] = explorer_details

        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

        return dictWindowDetails


    def MSBShouldExclude(window_title, exe_path, exclusion_rules):
        """
        Check if the window should be excluded based on title, exe name, or combination of both.
        """
        try:
            # Check if the window title is in the exclusion list
            if window_title in exclusion_rules["exclude_window_titles"]:
                return True

            # Check if the executable name is in the exclusion list
            for exeName in exclusion_rules["exclude_exe_names"]:
                if exeName in exe_path:
                    return True

            # Check for combinations of window title and executable name
            for combination in exclusion_rules["exclude_combination"]:
                if combination["window_title"] == window_title and combination["exe_name"] in exe_path:
                    return True

            return False
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())
            return False
    
    def MSGetExplorerDetails(active_hwnd):
        """This function will retrieve detailed information about the currently active folder in Windows Explorer."""
        explorer_details = ""
        try:
            # Use COM to interact with Windows Explorer
            shell = win32com.client.Dispatch("Shell.Application")
            windows = shell.Windows()

            for window in windows:
                # Match the handle of the Explorer window with the currently active window
                if window and int(window.HWND) == active_hwnd:
                    folder = window.Document.Folder  # Get the currently opened folder
                    folder_path = folder.Self.Path   # Get the path to the folder
                    explorer_details = f"Active Folder: {folder_path}"

                    # You can also get more details like folder items
                    # items = window.Document.Folder.Items()
                    # item_names = [item.Name for item in items]
                    # explorer_details += f"\nItems: {', '.join(item_names)}"
                    break  # Stop after finding the correct active File Explorer window

        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

        return explorer_details

    @staticmethod
    def MSSaveMultMonitorScreenShot(filename):
        """Takes a screenshot, saves it as a PNG, compresses it to JPEG, encrypts it, and removes the PNG."""
        try:
            # Take a screenshot of all monitors
            entire_screen = getScreenAsImage()

            # Save the screenshot as a PNG first
            temp_png = filename[:-4] + '.png'
            entire_screen.save(temp_png, format='PNG')

            # Open the PNG, convert to JPEG, and compress
            with Image.open(temp_png) as im:
                rgb_im = im.convert('RGB')  # Convert to RGB for JPEG format
                rgb_im.save(filename, format='JPEG', quality=100)  # Save as JPEG with maximum quality

            # Encrypt the JPEG file
            CEncDecHelper.MSEncryptFile(filename, strKey)

            # Remove the temporary PNG file
            os.remove(temp_png)

            print(f"Screenshot saved and encrypted successfully as {filename}.")
        
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

    
    @staticmethod
    def MSSaveScreenshot(path: str, file_prefix: str, file_postfix: str) -> Tuple[str, str]:
        """
        Saves a screenshot with a filename based on the current time.

        Args:
            path (str): The directory path where the screenshot will be saved.
            file_prefix (str): The prefix to add to the screenshot's filename.
            file_postfix (str): The postfix to add to the screenshot's filename.

        Returns:
            Tuple[str, str]: The current time as a formatted string and the filename used.
        """
        prefix = path + file_prefix
        current_time_str, filename = CMonHelper.MSGetCurTimeAsFileName(prefix, file_postfix)
        CMonHelper.MSSaveMultMonitorScreenShot(filename)
        return current_time_str, filename

    @staticmethod
    def MSGetCurTimeAsFileName(prefix: Optional[str] = None, postfix: Optional[str] = None) -> Tuple[str, str]:
        """
        Generates a filename based on the current time.

        Args:
            prefix (Optional[str]): A prefix to add to the filename.
            postfix (Optional[str]): A postfix to add to the filename.

        Returns:
            Tuple[str, str]: The current time as a formatted string and the generated filename.
        """
        current_time_str = CDateTimeHelper.MSGetCurrentTime()
        formatted_time = current_time_str.replace(' ', '_').replace(',', '_').replace(':', '_')
        
        if prefix:
            formatted_time = prefix + formatted_time
        if postfix:
            formatted_time += postfix

        return current_time_str, formatted_time

    @staticmethod
    def MSSaveDataFrameAsCSV(df: pd.DataFrame, path: str) -> None:
        """
        Saves a DataFrame to a CSV file. If the file already exists, it checks if the file contains the correct headers.
        Appends data without headers if the headers are correct, otherwise writes the DataFrame with headers.

        Args:
            df (pd.DataFrame): The DataFrame to save.
            path (str): The directory path where the CSV file will be saved.

        Raises:
            ValueError: If the DataFrame columns do not match the expected headers.
        """
        try:
            file_path = os.path.join(path, 'Log.csv')
            expected_headers = ['PCName', 'WindowTitle', 'ExeLocation', 'BreakDetails', 'ImageLocation', 'LogTime', 'ISDNDActive']

            if os.path.isfile(file_path):
                with open(file_path, 'r') as file:
                    first_line = file.readline().strip()
                
                if first_line == ','.join(expected_headers):
                    # Append without headers if headers are correct
                    df.to_csv(file_path, mode='a', header=False, index=False)
                else:
                    # Overwrite the file with correct headers and append data
                    df.to_csv(file_path, mode='w', header=True, index=False)
            else:
                # Create file and write with headers if file does not exist
                df.to_csv(file_path, mode='w', header=True, index=False)
        except Exception as e:
            try:
                file_path = os.path.join(path, 'Log.csv')
                df.to_csv(file_path, mode='a', header=False, index=False)
            except Exception as innerE:
                CLogger.MSwriteLog(traceback.format_exc())


    @staticmethod
    def MSCreateFolder():
        user = os.getlogin().lower()
        ist_timezone = pytz.timezone("Asia/Kolkata")
        obj = datetime.datetime.now(ist_timezone)
        
        # Get the device (computer) name using MSGetComputerName
        device_name = CMonHelper.MSGetComputerName()
        
        # Set a default path
        default_path = "\\\\192.168.1.18\\developer_public\\mon\\Data\\"
        
        # Initialize path variable
        path = ""
        
        # Check if the configuration file exists
        if os.path.exists("files\\Popup_Gui_Config.json"):
            try:
                dict_config = CMonHelper.MSReadJSON("files\\Popup_Gui_Config.json")
                    
                # Check if the user data file path exists in the configuration
                if "UserData_filepath" in dict_config and os.path.exists(dict_config["UserData_filepath"]):
                    dict_UserData = CMonHelper.MSReadJSON(dict_config["UserData_filepath"])

                    # Construct the path using the 'DataStore_path' from the config, append the device name after the date folder
                    path = os.path.join(dict_UserData[user]['DataStore_path'], user, "{0}_{1}_{2}".format(obj.year, obj.month, obj.day), device_name)

            except KeyError:
                print("DataStore_path not found in the configuration. Using default path.")
                path = os.path.join(dict_UserData['defaultuser']['DataStore_path'], user, "{0}_{1}_{2}".format(obj.year, obj.month, obj.day), device_name)

            except Exception as e:
                print(f"An error occurred: {e}. Using default path.")
                path = os.path.join(dict_UserData['defaultuser']['DataStore_path'], user, "{0}_{1}_{2}".format(obj.year, obj.month, obj.day), device_name)

        else:
            # If the configuration file doesn't exist, use the default path
            print("Configuration file not found. Using default path.")
            path = os.path.join(default_path, user, "{0}_{1}_{2}".format(obj.year, obj.month, obj.day), device_name)
        
        # Create the directory if it doesn't exist
        if not os.path.exists(path):
            os.makedirs(path)
        return path, user
    
    @staticmethod
    def MSHasMouseMoved(prevPosition):
        try:
            currPosition = pyautogui.position()
            if (prevPosition.x == currPosition.x) and (prevPosition.y == currPosition.y):
                return False, prevPosition
            else:
                prevPosition = currPosition
                return True, prevPosition
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())
            return False, prevPosition  
        
    @staticmethod
    def MSReadJSON(filepath):
        """Load a JSON file and return its content as a dictionary."""
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except Exception as e:           
            CLogger.MSwriteLog(traceback.format_exc())
            return {}
    

    def MSHasSufficientSpace(path, required_space_gb):
        """
        Check if the drive where the provided path resides has at least `required_space_gb` free space.
        
        :param path: The path where to check the drive space (e.g., 'd:\\abc\\asd\\asd').
        :param required_space_gb: The required space in gigabytes.
        :return: True if sufficient space is available, False otherwise.
        """
        try:
            # Get the drive (e.g., 'D:\')
            # drive = os.path.splitdrive(path)[0] + "\\"
            
            # Get disk usage statistics (total, used, free)
            total, used, free = shutil.disk_usage(path)
            
            # Convert free space to GB
            # free_gb = free / (1024 ** 3)
            # Convert free space to MB
            free_mb = free / (1024 ** 2)  # 1 MB = 1024 KB = 1024 * 1024 Bytes
            # free_mb = 0.5 for test 
            print(f"Free space: {free_mb:.2f} MB (Required: {required_space_gb} MB)")

            # Check if free space is greater than or equal to required space
            if free_mb >= required_space_gb:
                return True
            else:
                return False
        
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

            return False
    
    @staticmethod
    def MSSendInsufficientSpaceNotification(strDeviceName, strFilePath):
        try:
            dictMonConfig = CMonHelper.MSReadJSON("files\\Popup_Gui_Config.json")
            to_emails = dictMonConfig.get("Receivers", [])
            
            strSubject = f"Urgent: Monitoring Suspended on {strDeviceName} Due to Low Storage Space"
            strBody = (
                        f"Dear Team,\n\n"
                        f"The monitoring process on device '{strDeviceName}' has been halted due to insufficient storage space at Location '{strFilePath}'. "
                        "Please address this issue promptly to resume normal operations.\n\n"
                        "If you need assistance, please contact support.\n\n"
                        "Best regards,\n"
                        "Monitoring Team"
                    )
            CMonHelper.MSSendEmail(subject=strSubject, body=strBody)
        except Exception:
            CLogger.MSwriteLog(traceback.format_exc())

            
    @staticmethod
    def MSSendEmail(to_emails: List[str] = [], subject: str = "", body: str = ""):
        server = None  # Initialize server variable
        try:
            # Read the email configuration from a JSON file
            dictMonConfig = CMonHelper.MSReadJSON("files\\Popup_Gui_Config.json")
            email_config = dictMonConfig["Email_Config"]

            # If no recipients are provided, use the default receivers from the configuration
            if not to_emails:
                to_emails = to_emails or dictMonConfig.get("Receivers", [])

            # Set up the SMTP server
            if email_config["MAIL_SSL"]:
                server = smtplib.SMTP_SSL(email_config["MAIL_SERVER"], email_config["MAIL_PORT"])
            else:
                server = smtplib.SMTP(email_config["MAIL_SERVER"], email_config["MAIL_PORT"])
                if email_config["MAIL_TLS"]:
                    server.starttls()

            # Login to the SMTP server
            server.login(email_config["MAIL_USERNAME"], email_config["MAIL_PASSWORD"])

            # Create the email
            msg = MIMEMultipart()
            msg['From'] = f"{email_config['MAIL_FROM_NAME']} <{email_config['MAIL_FROM']}>"
            msg['Subject'] = subject
            msg['To'] = ', '.join(to_emails)  # Join the recipient emails into a single string
            msg.attach(MIMEText(body, 'plain'))

            # Send the email in one go to all recipients
            server.sendmail(email_config['MAIL_FROM'], to_emails, msg.as_string())

        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

        finally:
            if server:
                try:
                    server.quit()
                except Exception as e:
                    CLogger.MSwriteLog(traceback.format_exc())

    @staticmethod
    def decrypt_image(image_path, encryption_key):
        """
        Decrypts the given image file using the encryption helper.
        
        :param image_path: Path to the encrypted image file.
        :param encryption_key: Key used for encryption and decryption.
        :return: Path to the decrypted image or None if decryption fails.
        """
        try:
            CEncDecHelper.MSDecryptFile(image_path, encryption_key)
            return image_path  # Return the decrypted image path
        except Exception as e:
            CLogger.MSwriteLog(f"Error decrypting image: {traceback.format_exc()}")
            return None
    
    @staticmethod
    def crop_image_for_browser(image_path, window_rect):
        """
        Crops the given image for browser URL extraction based on predefined browser window coordinates,
        then saves the cropped image with '_cropped' added before the file extension.
        
        :param image_path: Path to the decrypted image file.
        :param window_rect: Tuple (left, top, right, bottom) representing window coordinates.
        :return: Cropped Image object or None.
        """
        try:
            # Load the image
            with Image.open(image_path) as img:
                # Crop the top area where the browser's address bar would be
                left, top, right, bottom = window_rect
                cropped_img = img.crop((left, top + 50, right, top + 80))  # Adjust based on browser address bar location
                
                # Create the save path by adding '_cropped' before the file extension
                base, ext = os.path.splitext(image_path)
                save_path = f"{base}_cropped{ext}"
                
                # Save the cropped image
                cropped_img.save(save_path)
                
                return cropped_img, save_path
        except Exception as e:
            CLogger.MSwriteLog(f"Error cropping and saving image: {traceback.format_exc()}")
            return None, ""

    
    @staticmethod
    def preprocess_image(cropped_img):
        """
        Preprocess the image to improve OCR accuracy.
        :param cropped_img: Cropped Image object.
        :return: Preprocessed image ready for OCR.
        """
        # Convert PIL image to OpenCV format
        open_cv_image = np.array(cropped_img)
        # Convert RGB to BGR for OpenCV
        open_cv_image = open_cv_image[:, :, ::-1].copy()
        
        # Convert to grayscale
        gray = cv2.cvtColor(open_cv_image, cv2.COLOR_BGR2GRAY)
        
        # Apply thresholding to binarize the image
        _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY)

        # Convert back to PIL format for pytesseract processing
        return Image.fromarray(thresh)

    # @staticmethod
    # def extract_url_from_image(cropped_img):
    #     """
    #     Extracts and validates a URL from a cropped image using OCR (pytesseract).
        
    #     :param cropped_img: Cropped Image object.
    #     :return: Extracted and validated URL or None.
    #     """
    #     try:
    #         # Step 1: Preprocess the image for better OCR results
    #         preprocessed_image = CMonHelper.preprocess_image(cropped_img)

    #         # Step 2: Perform OCR with pytesseract, limiting the character set
    #         ocr_text = pytesseract.image_to_string(preprocessed_image, config='--psm 6')

    #         # Step 3: Use a regular expression to find potential URLs
    #         url_pattern = r"(https?://[^\s]+)"
    #         matches = re.findall(url_pattern, ocr_text)

    #         # Step 4: Validate and return the first valid URL, if any
    #         for match in matches:
    #             parsed_url = urlparse(match)
    #             if parsed_url.scheme and parsed_url.netloc:
    #                 return match.strip()  # Return the cleaned-up valid URL

    #         # Return None if no valid URL is found
    #         return None
    #     except Exception as e:
    #         # Log the exception with full traceback details
    #         CLogger.MSwriteLog(f"Error extracting URL from image: {traceback.format_exc()}")
    #         return None

    @staticmethod
    def encrypt_image(image_path, encryption_key):
        """
        Encrypts the given image file using the encryption helper.
        
        :param image_path: Path to the decrypted image file.
        :param encryption_key: Key used for encryption.
        :return: Boolean indicating success or failure.
        """
        try:
            CEncDecHelper.MSEncryptFile(image_path, encryption_key)
            return True
        except Exception as e:
            CLogger.MSwriteLog(f"Error encrypting image: {traceback.format_exc()}")
            return False

    @staticmethod
    def getBrowserURL(dictActiveWindowDetails, image_path, encryption_key):
        """
        Orchestrates the decryption, cropping, OCR, and re-encryption process to extract the URL from a browser window.
        
        :param dictActiveWindowDetails: Dictionary containing active window details (includes window rectangle).
        :param image_path: Path to the encrypted screenshot image.
        :param encryption_key: Encryption key used for decrypting and re-encrypting the image.
        :return: Extracted URL as a string or None if URL could not be extracted.
        """
        try:
            lsBrowsers = ["brave.exe", "chrome.exe", "firefox.exe", "msedge.exe", "opera.exe"]
            # Check if the active window is a browser
            for strBrowser in lsBrowsers:
                if strBrowser in dictActiveWindowDetails["ExeLocation"].lower():
                    
                    # Step 1: Decrypt the image
                    decrypted_image_path = CMonHelper.decrypt_image(image_path, encryption_key)
                    if decrypted_image_path:
                        
                        # Step 2: Crop the browser address bar from the image
                        cropped_img, cropped_img_path =  CMonHelper.crop_image_for_browser(decrypted_image_path, dictActiveWindowDetails["WindowRect"])
                        if cropped_img:
                            
                            # Step 3: Extract the URL using OCR
                            # url =  CMonHelper.extract_url_from_image(cropped_img)
                            
                            # Step 4: Re-encrypt the image after processing
                            encrypt_success =  CMonHelper.encrypt_image(decrypted_image_path, encryption_key)
                            CMonHelper.encrypt_image(cropped_img_path, encryption_key)
                            # Step 5: Return the extracted URL
                            # if encrypt_success:
                            #     return url
                            
                        else:
                            CLogger.MSwriteLog("Image cropping failed.")
                    else:
                        CLogger.MSwriteLog("Decryption failed.")
        except Exception as e:
            CLogger.MSwriteLog(f"Error in getBrowserURL: {traceback.format_exc()}")

        # If any step fails, return None
        return None
      
class CDateTimeHelper:

    @staticmethod
    def MSGetCurrentTime() -> str:
        """
        Returns the current time as a formatted string.
        Example format: Sat, 27 Nov 2010 04:35:00 +0000
        """
        time_str = ""
        try:
            ist_timezone = pytz.timezone("Asia/Kolkata")
            current_time = datetime.datetime.now(ist_timezone)
            time_str = current_time.strftime("%a, %d %b %Y %H:%M:%S")
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())

        return time_str


class CEncDecHelper:

    @staticmethod
    def MSEncryptFile(filename, strKey):
        """Encrypts the file specified by filename using the provided key."""
        try:
            # Initialize the Fernet cipher suite with the provided key
            key = strKey.encode() if isinstance(strKey, str) else strKey
            cipher_suite = Fernet(key)
            
            # Read the file data
            with open(filename, 'rb') as file:
                file_data = file.read()
            
            # Encrypt the data
            encrypted_data = cipher_suite.encrypt(file_data)
            
            # Write the encrypted data back to the file
            with open(filename, 'wb') as file:
                file.write(encrypted_data)
            
            print(f"The file {filename} has been encrypted successfully.")
        
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())


    @staticmethod
    def MSDecryptFile(filename, strKey):
        """Decrypts the file specified by filename using the provided key."""
        try:
            # Initialize the Fernet cipher suite with the provided key
            key = strKey.encode() if isinstance(strKey, str) else strKey
            cipher_suite = Fernet(key)
            
            # Read the encrypted data from the file
            with open(filename, 'rb') as file:
                encrypted_data = file.read()
            
            # Decrypt the data
            decrypted_data = cipher_suite.decrypt(encrypted_data)
            
            # Write the decrypted data back to the file
            with open(filename, 'wb') as file:
                file.write(decrypted_data)
            
            print(f"The file {filename} has been decrypted successfully.")
        
        except Exception as e:
            CLogger.MSwriteLog(traceback.format_exc())


class CLogger:
    _loggers = {}  # Store loggers per user to prevent multiple instances

    @staticmethod
    def MSSetupLogging(log_userwise=False, userName=""):
        """Set up logging configuration to write logs in the logs directory."""
        logger_name = f"CustomLogger_{userName}" if log_userwise and userName else "CustomLogger"

        # Prevent duplicate loggers
        if logger_name in CLogger._loggers:
            return CLogger._loggers[logger_name]

        # Create 'logs' directory if it doesn't exist
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # Get the current date for the log file name
        ist_timezone = pytz.timezone("Asia/Kolkata")
        current_date = datetime.datetime.now(ist_timezone).strftime("%d_%m_%Y")

        if log_userwise and userName:
            log_filename = f"Log_{current_date}_{userName}.txt"
        else:
            log_filename = f"Log_{current_date}.txt"

        log_filepath = os.path.join(log_dir, log_filename)

        # Create a custom logger
        logger = logging.getLogger(logger_name)
        logger.handlers.clear()  # Remove existing handlers to avoid duplication

        # Create a file handler
        file_handler = logging.FileHandler(log_filepath)
        file_handler.setLevel(logging.DEBUG)

        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)  # Show INFO+ logs in console

        # Create a logging format
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add the handlers to the logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.DEBUG)  # Log only errors and above

        CLogger._loggers[logger_name] = logger  # Store the logger to avoid duplicates
        return logger

    @staticmethod
    def MSwriteLog(strLogMessage, level="error", log_userwise=False, userName=""):
        """Logs the message to the log file."""
        try:
            # Get the custom logger
            logger = CLogger.MSSetupLogging(log_userwise, userName)

            log_levels = {
                "info": logging.INFO,
                "warning": logging.WARNING,
                "error": logging.ERROR,
                "debug": logging.DEBUG
            }
            level = log_levels.get(level.lower(), logging.ERROR)

            # Log the message
            logger.log(level, strLogMessage)

        except Exception as e:
            # Fallback in case logging fails
            print(f"Logging failed: {e}")

if __name__ == "__main__":
    try:
        computer_name = CMonHelper.MSGetComputerName()
        username = CMonHelper.MSGetLoggedUserName()
        print(f"Computer Name: {computer_name}")
        print(f"Logged-in User: {username}")
    except Exception as e:
        print(f"An error occurred: {e}")
