import os
import time as ti
import pandas as pd
import json
import traceback
import pythoncom
import requests
from threading import Thread
from datetime import date
from MouseTracker import MouseActivityTracker
from KeyboardTracker import KeyboardMonitor
from DataSenderV2 import create_zip_and_send,is_internet_available,activate_admin_mode,remove_old_logs
from PyQt5.QtWidgets import (
    QApplication, QSystemTrayIcon, QMenu, QAction, QDialog,
    QVBoxLayout, QLabel, QPushButton, QGraphicsOpacityEffect, QGraphicsDropShadowEffect,QMessageBox
)
from PyQt5.QtGui import QIcon, QImageReader, QFont, QColor
from PyQt5.QtCore import QTimer, Qt, QObject, pyqtSignal, pyqtSlot, QMetaObject, QPropertyAnimation
from monHelper import CMonHelper as helper, CD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>elper, CLogger
from PopUpPyQTV2 import create_break_popup
import PopUpPyQTV2 as popupCode
from key import str<PERSON><PERSON>
from HalfDayEmail import process_break_details
from HIDActivityTracker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ctivityTracker

# Dialog shown when DND ends
class DND_EndDialog(QDialog):
    def __init__(self, total_time_spent, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Do Not Disturb Ended")
        self.setModal(True)  # Make modal so user must acknowledge
        self.setWindowFlag(Qt.WindowCloseButtonHint, False)  # Disable close button
        self.setWindowFlag(Qt.WindowMinimizeButtonHint, False)  # Disable minimize button
        
        # Set up fade-in animation
        self.opacity_effect = QGraphicsOpacityEffect(self)
        self.setGraphicsEffect(self.opacity_effect)
        self.opacity_effect.setOpacity(0)  # Start fully transparent
        
        self.animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.animation.setDuration(700)  # Slightly longer for elegance
        self.animation.setStartValue(0)  # From transparent
        self.animation.setEndValue(1)  # To fully opaque
        
        # Set up UI
        self.init_ui(total_time_spent)
        
        # Apply modern styling with gradient and shadow
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, 
                                            stop:0 #e0f7fa, stop:1 #b2ebf2);
                border-radius: 15px;
                border: 1px solid #80deea;
            }
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #006064;
                background: transparent;
            }
            QPushButton {
                background-color: #00acc1;
                color: white;
                border: none;
                padding: 12px;
                font-size: 16px;
                border-radius: 8px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #00838f;
                transition: background-color 0.3s;
            }
            QPushButton:pressed {
                background-color: #006064;
            }
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 80))
        self.setGraphicsEffect(shadow if not self.graphicsEffect() else self.opacity_effect)

    def init_ui(self, total_time_spent):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)  # Larger margins for breathing room
        layout.setSpacing(20)  # Increased spacing for elegance
        
        # Create and style the message label
        message = f"Do Not Disturb mode has ended.\nTime spent: {total_time_spent} minutes."
        label = QLabel(message)
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont("Helvetica", 16, QFont.Bold))  # Custom font for sophistication
        layout.addWidget(label)
        
        # Create and style the OK button
        ok_button = QPushButton("OK")
        ok_button.setFont(QFont("Helvetica", 14))
        ok_button.clicked.connect(self.accept)  # Closes dialog when clicked
        layout.addWidget(ok_button, alignment=Qt.AlignCenter)
        
        self.setLayout(layout)
        self.setFixedSize(450, 250)  # Slightly larger for a refined look

    def showEvent(self, event):
        super().showEvent(event)
        self.animation.start()  # Start animation when dialog is shown

    def closeEvent(self, event):
        event.ignore()  # Prevent closing dialog except via OK button

class SystemTrayApp(QObject):
    show_popup_signal = pyqtSignal(int, str, dict, dict, dict, dict, str, bool, bool)
    dnd_ended_signal = pyqtSignal(int)  # Signal to handle DND end in main thread
    
    last_api_call_date = None

    def __init__(self, user, dict_config, dict_users_data, dict_general_account, dict_customer_data, strPCName):
        super().__init__()
        self.user = user
        self.dict_config = dict_config
        self.dictUsersData = dict_users_data
        self.dictGeneralAccount = dict_general_account
        self.dictCustomerData = dict_customer_data
        self.strPCName = strPCName

        self.dnd_active = False
        self.dnd_start_time = None
        self.popup_active = False  # Add this flag
        self.main_loop_timestamp_ref = None
        self.last_user_activity_time_ref = None
        self.show_popup_signal.connect(self.show_break_popup)
        

        # Initialize QApplication
        self.app = QApplication([])
        self.app.setQuitOnLastWindowClosed(False)

        # Set up the system tray icon
        current_dir = os.path.dirname(os.path.abspath(__file__))
        icon_path = os.path.join(current_dir,'files', 'Project_MON_icon.ico')
        if b"webp" not in QImageReader.supportedImageFormats():
            print("WebP not supported, using default PNG icon.")
            icon_path = os.path.join(current_dir, 'icon.png')
        self.tray_icon = QSystemTrayIcon(QIcon(icon_path), parent=self.app)
        self.tray_menu = QMenu()
        self.create_actions()
        self.create_tray_icon()

        # DND timer in main thread
        self.dnd_timer = QTimer()
        self.dnd_timer.setSingleShot(True)
        self.dnd_timer.timeout.connect(self.end_dnd)

        # Connect DND end signal to dialog display
        self.dnd_ended_signal.connect(self.show_dnd_end_dialog)
        self.close_dnd_action.setEnabled(True)
        
        self.fetch_admin_data_on_startup()
        self.last_api_call_date = date.today()
        self.date_check_timer = QTimer()
        self.date_check_timer.timeout.connect(self.check_date_for_api_call)
        self.date_check_timer.start(3600000)  # Check every hour (3600000 ms)
        
    def fetch_admin_data_on_startup(self):
        """Fetch admin data when application starts"""
        try:
            CLogger.MSwriteLog("Fetching admin data on startup", "info", log_userwise=True, userName=self.user)
            # activate_admin_mode(self)
            
            # Add log cleanup on startup
            self.cleanup_old_logs()
        except Exception as e:
            CLogger.MSwriteLog(f"Error fetching admin data on startup: {str(e)}", "error", log_userwise=True, userName=self.user)
    
    def check_date_for_api_call(self):
        """Check if date has changed and call API if needed"""
        today = date.today()
        if self.last_api_call_date != today:
            try:
                CLogger.MSwriteLog("Daily API call triggered", "info", log_userwise=True, userName=self.user)
                # activate_admin_mode(self)
                
                # Add log cleanup when date changes
                self.cleanup_old_logs()
                
                self.last_api_call_date = today
            except Exception as e:
                CLogger.MSwriteLog(f"Error in daily API call: {str(e)}", "error", log_userwise=True, userName=self.user)
    
    def cleanup_old_logs(self):
        try:
            # Get the user's data store path
            if self.user in self.dictUsersData and "DataStore_path" in self.dictUsersData[self.user]:
                data_store_path = self.dictUsersData[self.user]["DataStore_path"]
                user_folder_path = os.path.join(data_store_path, self.user)
                
                CLogger.MSwriteLog("Running scheduled log cleanup", "info", log_userwise=True, userName=self.user)
                remove_old_logs(user_folder_path)
        except Exception as e:
            CLogger.MSwriteLog(f"Error in log cleanup: {str(e)}", "error", log_userwise=True, userName=self.user)
        
    def create_actions(self):
        self.dnd_30min_action = QAction('Do Not Disturb for 30 minutes')
        self.dnd_30min_action.triggered.connect(lambda: self.start_dnd(30))  # Fixed from 1 to 10
        self.dnd_1hour_action = QAction('Do Not Disturb for 1 hour')
        self.dnd_1hour_action.triggered.connect(lambda: self.start_dnd(60))
        self.dnd_90min_action = QAction('Do Not Disturb for 1.5 hour')
        self.dnd_90min_action.triggered.connect(lambda: self.start_dnd(90))
        self.dnd_2hours_action = QAction('Do Not Disturb for 2 hours')
        self.dnd_2hours_action.triggered.connect(lambda: self.start_dnd(120))
        self.dnd_180min_action = QAction('Do Not Disturb for 2.5 hour')
        self.dnd_180min_action.triggered.connect(lambda: self.start_dnd(180))
        self.close_dnd_action = QAction('Close DND')
        self.close_dnd_action.triggered.connect(self.close_dnd)

        # Half Day Leave action
        self.half_day_leave_action = QAction('Half Day Leave')
        self.half_day_leave_action.triggered.connect(self.show_half_day_leave_popup)
        
        # Admin mode action
        self.admin_mode_action = QAction("Admin Mode", self)
        self.admin_mode_action.triggered.connect(lambda: activate_admin_mode(self))

        # Exit action
        self.quit_action = QAction('Exit', self)
        self.quit_action.triggered.connect(self.tray_menu.hide)

    def create_tray_icon(self):
        # Create a submenu for DND options
        self.dnd_submenu = QMenu('Do Not Disturb')
        self.dnd_submenu.addAction(self.dnd_30min_action)
        self.dnd_submenu.addAction(self.dnd_1hour_action)
        self.dnd_submenu.addAction(self.dnd_90min_action)
        self.dnd_submenu.addAction(self.dnd_2hours_action)
        self.dnd_submenu.addAction(self.dnd_180min_action)
        self.dnd_submenu.addSeparator()
        self.dnd_submenu.addAction(self.close_dnd_action)

        # Add the DND submenu to the main menu
        self.tray_menu.addMenu(self.dnd_submenu)

        # Add Half Day Leave option 
        self.tray_menu.addAction(self.half_day_leave_action)
        
        # Add Admin Mode option
        self.tray_menu.addAction(self.admin_mode_action)

        self.tray_menu.addSeparator()
        self.tray_menu.addAction(self.quit_action)
        self.tray_icon.setContextMenu(self.tray_menu)
        self.tray_icon.show()
        self.tray_icon.activated.connect(self.on_tray_icon_click)

    def on_tray_icon_click(self, reason):
        if reason == QSystemTrayIcon.Trigger:
            self.tray_menu.exec_(self.tray_icon.geometry().topLeft())

    def start_dnd(self, minutes):
        self.dnd_duration = minutes * 60 * 1000  # Convert to milliseconds
        self.dnd_active = True
        self.dnd_start_time = ti.time()
        self.dnd_timer.start(self.dnd_duration)
        self.close_dnd_action.setEnabled(True)
        CLogger.MSwriteLog(f"Do Not Disturb started for {minutes} minutes.", "info")
        print(f"Do Not Disturb started for {minutes} minutes.")

    def end_dnd(self):
        if self.dnd_start_time is not None:
            time_spent_seconds = ti.time() - self.dnd_start_time
            total_time_spent = round(time_spent_seconds / 60)  # Convert to minutes
        else:
            total_time_spent = 0
        self.dnd_active = False
        self.close_dnd_action.setEnabled(False)  # Disable "Close DND" option
        self.dnd_start_time = None
        CLogger.MSwriteLog(f"Do Not Disturb ended. Time spent: {total_time_spent} minutes.", "info")
        print(f"Do Not Disturb ended. Time spent: {total_time_spent} minutes.")
        self.dnd_ended_signal.emit(total_time_spent)
        if self.last_user_activity_time_ref is not None:
            self.last_user_activity_time_ref[0] = ti.time()
    def close_dnd(self):
        if self.dnd_active:
            self.dnd_timer.stop()  # Stop the timer immediately
            self.end_dnd()  
        else:
            print("DND is not active, but Close DND was clicked")          

    def show_half_day_leave_popup(self):
        try:
            CLogger.MSwriteLog("Half Day Leave option selected", "info", log_userwise=True, userName=self.user)
            self.show_break_popup(
                minutes=255,  # Set to 0 to allow user to enter their own time
                user=self.user,
                dict_config=self.dict_config,
                dict_users_data=self.dictUsersData,
                dict_general_account=self.dictGeneralAccount,
                dict_customer_data=self.dictCustomerData,
                strPCName=self.strPCName,
                after_dnd=False,
                is_half_day_leave=True  # New parameter to indicate Half Day Leave
            )
        except Exception as e:
            CLogger.MSwriteLog(f"Error showing Half Day Leave popup: {traceback.format_exc()}", "error", log_userwise=True, userName=self.user)

    @pyqtSlot(int)
    def show_dnd_end_dialog(self, total_time_spent):
        try:
            self.show_break_popup(
                minutes=total_time_spent,
                user=self.user,
                dict_config=self.dict_config,
                dict_users_data=self.dictUsersData,
                dict_general_account=self.dictGeneralAccount,
                dict_customer_data=self.dictCustomerData,
                strPCName=self.strPCName,    
                after_dnd=True,
                is_half_day_leave=False
            )
            print("DND dialog closed.")
            CLogger.MSwriteLog("DND mode ended. Resuming normal logging.", "info")
        except Exception as e:
            CLogger.MSwriteLog(f"Error displaying DND end dialog: {traceback.format_exc()}", "error")

    @pyqtSlot(result=dict)
    def get_active_window_details(self):
        result = helper.MSGetActiveWindowDetails(
            dictExclusionData={
                "exclude_window_titles": [],
                "exclude_exe_names": [],
                "exclude_combination": []
            }
        )
        return result if result is not None else {"WindowTitle": "", "ExeLocation": ""}

    @pyqtSlot(int, str, dict, dict, dict, dict, str, bool, bool)
    def show_break_popup(self, minutes, user, dict_config, dict_users_data, dict_general_account, dict_customer_data, strPCName, after_dnd=False, is_half_day_leave=False):
        try:
            self.popup_active = True  # Set flag before showing popup
            self.dictUsersData = helper.MSReadJSON(dict_config["UserData_filepath"])
            self.dictCustomerData = helper.MSReadJSON(dict_config["CustomerData_filepath"])
            break_details = popupCode.create_break_popup(
                minutes,
                user,
                dicMonConfig=dict_config,
                dictUsersData=self.dictUsersData,
                dictGeneralAccount=dict_general_account,
                dictCustomerData=self.dictCustomerData,
                after_dnd=after_dnd,
                is_half_day_leave=is_half_day_leave
            )
            # break_details is the result from the popup (dict or None)
            
            if break_details is not None:
                # Log the popup details into a DataFrame
                df1 = pd.DataFrame()
                df1 = pd.concat([df1, pd.DataFrame({
                    'PCName': [strPCName],
                    'WindowTitle': [""],
                    'ExeLocation': [""],
                    'BreakDetails': [break_details],
                    'ImageLocation': [""],
                    'LogTime': [DateTimeHelper.MSGetCurrentTime()],
                    'ISDNDActive' : [after_dnd]
                })], ignore_index=True)
                
                # Save the DataFrame to CSV (adjust path as needed)
                path, _ = helper.MSCreateFolder()
                helper.MSSaveDataFrameAsCSV(df=df1, path=path)
                
                # Place the call here to process break details and send email if needed
                process_break_details(self.user, break_details, self.dictUsersData)
        
                CLogger.MSwriteLog(f"Break popup completed. Details: {break_details}", "info", log_userwise=True, userName=user)
            else:
                CLogger.MSwriteLog("Popup was closed without submitting.", "warning", log_userwise=True, userName=user)
                
            self.popup_active = False
            if self.last_user_activity_time_ref is not None:
                self.last_user_activity_time_ref[0] = ti.time()
                
        except Exception as e:
            CLogger.MSwriteLog(f"Error showing break popup: {traceback.format_exc()}", "error", log_userwise=True, userName=user)
            self.popup_active = False

def monitoring_loop(system_tray_app):
    try:
        pythoncom.CoInitialize()
    except Exception as e:
        CLogger.MSwriteLog(f"Error initializing COM: {e}", "error", log_userwise=True, userName="unknown")

    path, user = helper.MSCreateFolder()
    strPCName = helper.MSGetComputerName()
    
    df1 = pd.DataFrame()
    obj_keyListener = KeyboardMonitor()
    obj_keyListener_thread = Thread(target=obj_keyListener.monitor)
    obj_keyListener_thread.start()
    mouse_tracker = MouseActivityTracker()

    # Initialize timing variables
    last_user_activity_time = [ti.time()]  # Use a list for reference sharing
    system_tray_app.last_user_activity_time_ref = last_user_activity_time  # Share with SystemTrayApp
    popup_shown = False
    log_count = 0
    imageLogCount = 0
    last_script_run_time = ti.time()

    # Load configuration (unchanged from your code)
    IBREAK_TIME_MIN = 10
    LOG_INTERVAL_SEC = 45
    iMinDiskSizeForSS = 10
    bSendEmailNotification = True
    IMAGE_LOG_MULTIPLE = 2
    INACTIVITY_THRESHOLD_MIN = 5
    SCRIPT_INTERVAL_SEC = 300
    bEnableTransfer = False

    try:
        with open("files\\Configurations.json", "r") as config_file:
            config = json.load(config_file)
            SCRIPT_INTERVAL_SEC = config["SCRIPT_INTERVAL_SEC"]
    except Exception as e:
        CLogger.MSwriteLog(f"Error loading Configurations.json: {traceback.format_exc()}", "error", log_userwise=True, userName="unknown")

    if os.path.exists("files\\Popup_Gui_Config.json"):
        try:
            dict_config = helper.MSReadJSON("files\\Popup_Gui_Config.json")
            if "UserData_filepath" in dict_config and os.path.exists(dict_config["UserData_filepath"]):
                dict_UserData = helper.MSReadJSON(dict_config["UserData_filepath"])
                user_data = dict_UserData.get(user, dict_UserData.get("defaultuser", {}))
                IBREAK_TIME_MIN = user_data["IBREAK_TIME_MIN"]
                LOG_INTERVAL_SEC = user_data["LOG_INTERVAL_SEC"]
                iMinDiskSizeForSS = user_data["MinSpaceInGB"]
                bSendEmailNotification = user_data["bSendEmailNotification"]
                IMAGE_LOG_MULTIPLE = user_data.get("IMAGE_LOG_MULTIPLE", 2)
                INACTIVITY_THRESHOLD_MIN = user_data.get("INACTIVITY_THRESHOLD_MIN", 5)
                bEnableTransfer = user_data.get("bEnableTransfer", False)
            if "CustomerData_filepath" in dict_config and os.path.exists(dict_config["CustomerData_filepath"]):
                dictCustomerData = helper.MSReadJSON(dict_config["CustomerData_filepath"])
            else:
                dictCustomerData = {}
            if "GeneralAccount_filepath" in dict_config and os.path.exists(dict_config["GeneralAccount_filepath"]):
                dict_GeneralAccount = helper.MSReadJSON(dict_config["GeneralAccount_filepath"])
            else:
                dict_GeneralAccount = {}
            if "Exclusion_filepath" in dict_config and os.path.exists(dict_config["Exclusion_filepath"]):
                dictExclusionData = helper.MSReadJSON(dict_config["Exclusion_filepath"])
            else:
                dictExclusionData = {}
        except Exception as e:
            CLogger.MSwriteLog(f"Error reading config: {traceback.format_exc()}", "error", log_userwise=True, userName=user)
    else:
        dict_config = {}
        dict_UserData = {}
        dict_GeneralAccount = {}
        dictCustomerData = {}
        dictExclusionData = {}
        
    if dict_UserData.get(user, {}).get("bEnableTouchScreenMonitoring", False):
        hid_tracker = HIDFallbackActivityTracker()
        
    modified_path = os.path.abspath(os.path.join(path, "..", ".."))
    bHasSufficientSpace = helper.MSHasSufficientSpace(path=modified_path, required_space_gb=iMinDiskSizeForSS)
    if not bHasSufficientSpace and bSendEmailNotification:
        helper.MSSendInsufficientSpaceNotification(strDeviceName=strPCName, strFilePath=path)

    # Helper function to log activity
    def log_activity(IsDNDActive = False):
        nonlocal log_count, df1, path,imageLogCount
        log_count += 1
        imageLogCount += 1
        path, user = helper.MSCreateFolder()
        dictActiveWindowDetails = helper.MSGetActiveWindowDetails(dictExclusionData=dictExclusionData)
        has_space = helper.MSHasSufficientSpace(path=modified_path, required_space_gb=iMinDiskSizeForSS)
        bCaptureScreenshot = dict_UserData.get(user, {}).get("bCaptureScreenshot", False)
        if isinstance(bCaptureScreenshot, str):
            bCaptureScreenshot = bCaptureScreenshot.lower() == "true"
        bCaptureScreenshot = bCaptureScreenshot and has_space

        if bCaptureScreenshot and dictActiveWindowDetails["WindowTitle"]:
            try:
                if imageLogCount % IMAGE_LOG_MULTIPLE == 0:
                    strLogTime, filename = helper.MSSaveScreenshot(path, file_prefix=f'\\{user}_', file_postfix='.jpg')
                    url = helper.getBrowserURL(dictActiveWindowDetails=dictActiveWindowDetails, image_path=filename, encryption_key=strKey)
                else:
                    filename = ""
                    strLogTime = DateTimeHelper.MSGetCurrentTime()
            except Exception as e:
                filename = ""
                strLogTime = DateTimeHelper.MSGetCurrentTime()
        else:
            filename = ""
            strLogTime = DateTimeHelper.MSGetCurrentTime()

        df1 = pd.concat([df1, pd.DataFrame({
            'PCName': [strPCName],
            'WindowTitle': [dictActiveWindowDetails["WindowTitle"]],
            'ExeLocation': [dictActiveWindowDetails["ExeLocation"]],
            'BreakDetails': [{}],
            'ImageLocation': [filename],
            'LogTime': [strLogTime],
            'ISDNDActive' : [IsDNDActive]
        })], ignore_index=True)

        print("---------------------------------------------------------")
        print(f"PCName: {strPCName}\nLogTime: {strLogTime}\nImageLocation: {filename}")
        print(f"WindowTitle: {dictActiveWindowDetails['WindowTitle']}")
        print(f"ExeLocation: {dictActiveWindowDetails['ExeLocation']}")
        print(f"ISDNDActive: {IsDNDActive}")
        print("__________________________________________________________")

        if len(df1) >= 1:
            helper.MSSaveDataFrameAsCSV(df=df1, path=path)
            df1 = pd.DataFrame()

    while True:
        try:
            ti.sleep(LOG_INTERVAL_SEC)
            if system_tray_app.popup_active:
                continue  # Skip iteration while popup is active
            currentTime = ti.time()
            if bEnableTransfer: 
                if (currentTime - last_script_run_time) >= SCRIPT_INTERVAL_SEC:
                    try:
                        CLogger.MSwriteLog("Calling external API","info",log_userwise=True,userName=user)
                        if is_internet_available():
                            print("Internet is available. Proceeding with ZIP creation...")
                            CLogger.MSwriteLog("Internet is available. Proceeding with ZIP creation...","info",log_userwise=True,userName=user)
                            create_zip_and_send()
                            # Call your ZIP creation function here
                        else:
                            print("No internet connection. Skipping ZIP creation.")
                            CLogger.MSwriteLog("No internet connection. Skipping ZIP creation.",log_userwise=True,userName=user)
                        last_script_run_time = currentTime  # Reset timer
                    except Exception as e:
                        print(f"Error while calling script: {e}")
                        CLogger.MSwriteLog(f"Error while calling script: {e}")

            # Fetch user-specific bCaptureScreenshot setting
            bCaptureScreenshot = dict_UserData.get(user, {}).get("bCaptureScreenshot", False)
            # Ensure it remains a boolean (not string)
            if isinstance(bCaptureScreenshot, str):
                    bCaptureScreenshot = bCaptureScreenshot.lower() == "true"
            # Check user activity
            mouse_active = mouse_tracker.compareCurrentVsLastPosition()
            keyboard_active = obj_keyListener.check_activity(iThreshold=INACTIVITY_THRESHOLD_MIN * 60)
            if dict_UserData.get(user, {}).get("bEnableTouchScreenMonitoring", False):
                if not (mouse_active or keyboard_active):
                    hid_active = hid_tracker.is_user_active(threshold_seconds=INACTIVITY_THRESHOLD_MIN * 60)
                else:
                    hid_active = False  # Already counted as active
            else:
                hid_active = None
            CLogger.MSwriteLog(f"Mouse active: {mouse_active}, Keyboard active: {keyboard_active}, HID active: {hid_active}", "info", log_userwise=True,userName=user)
            if mouse_active or keyboard_active or hid_active:
                last_user_activity_time[0] = currentTime
                popup_shown = False
                log_activity(IsDNDActive=system_tray_app.dnd_active)
                CLogger.MSwriteLog("User active, logging activity.", "info", log_userwise=True,userName=user)
            else:
                if system_tray_app.dnd_active:
                    log_activity(IsDNDActive=system_tray_app.dnd_active)
                    CLogger.MSwriteLog("Logging during DND.", "info", log_userwise=True, userName=user)
                else:
                    inactivity_duration = currentTime - last_user_activity_time[0]
                    if inactivity_duration < (INACTIVITY_THRESHOLD_MIN  * 60):
                        log_activity(IsDNDActive=system_tray_app.dnd_active)
                        CLogger.MSwriteLog(f"User inactive but within inactivity threshold, logging. {inactivity_duration}", "info", log_userwise=True, userName=user)
                    # 4c) Between inactivity threshold and break time → do _nothing_ (no log, no popup)
                    elif inactivity_duration < (IBREAK_TIME_MIN * 60):
                        # optional: a debug message
                        CLogger.MSwriteLog(f"User inactive beyond threshold; waiting for break time. {inactivity_duration}","info", log_userwise=True, userName=user)
                        continue
                    else:
                        print("User inactive, skipping log after break time.")
                        CLogger.MSwriteLog("User inactive, skipping log after break time.", "info", log_userwise=True, userName=user)
                        if not popup_shown:
                            minutes = int(inactivity_duration // 60)
                            print(f"Inactivity detected: {minutes} minutes")
                            CLogger.MSwriteLog(f"Break time in minutes: {minutes}", "info", log_userwise=True, userName=user)
                            system_tray_app.show_popup_signal.emit(
                                minutes, user, dict_config, dict_UserData,
                                dict_GeneralAccount, dictCustomerData, strPCName, False, False
                            )
                            popup_shown = True
        except Exception as e:
            CLogger.MSwriteLog(f"Main loop error: {traceback.format_exc()}", "error", log_userwise=True, userName=user)
            ti.sleep(5)

if __name__ == '__main__':
    # Initialize the helper module to get user and path
    path, user = helper.MSCreateFolder()
    strPCName = helper.MSGetComputerName()

    # Load configuration files
    try:
        dict_config = helper.MSReadJSON("files\\Popup_Gui_Config.json")
        dict_UserData = helper.MSReadJSON(dict_config["UserData_filepath"])
        dictCustomerData = helper.MSReadJSON(dict_config["CustomerData_filepath"])
        dict_GeneralAccount = helper.MSReadJSON(dict_config["GeneralAccount_filepath"])
    except Exception as e:
        CLogger.MSwriteLog(f"Error loading configurations: {e}", "error")
        dict_config = {}
        dict_UserData = {}
        dictCustomerData = {}
        dict_GeneralAccount = {}

    # Instantiate SystemTrayApp with required parameters
    system_tray_app = SystemTrayApp(
        user=user,
        dict_config=dict_config,
        dict_users_data=dict_UserData,
        dict_general_account=dict_GeneralAccount,
        dict_customer_data=dictCustomerData,
        strPCName=strPCName
    )

    # Start the monitoring thread
    monitoring_thread = Thread(target=monitoring_loop, args=(system_tray_app,))
    monitoring_thread.daemon = True
    monitoring_thread.start()

    # Start the Qt event loop
    system_tray_app.app.exec_()