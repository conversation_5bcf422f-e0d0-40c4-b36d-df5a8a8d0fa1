import os
import requests
import json
import traceback
import requests
import datetime
import zipfile
import socket
# import pandas as pd
from monHelper import CMonHelper as helper, CLogger
from PyQt5.QtWidgets import QMessageBox
import time
from datetime import timedelta
# import time
# import zipfile
# import hashlib
# from queue import Queue 
# import socket
# from validateUser import validateUser
# import threading
# import shutil


with open("files\\Configurations.json", "r") as config_file:
    config = json.load(config_file)

# Access variables
API_URL = config["API_URL"]
API_LOCAL_URL = config["API_URL_LOCAL"]
SENT_IMAGES_FILE = config["SENT_IMAGES_FILE"]
FAILED_UPLOADS_FILE = config["FAILED_UPLOADS_FILE"]
API_URL2 = config["API_URL2"]

def is_internet_available():
    try:
        # Try connecting to Google's public DNS server
        socket.create_connection(("*******", 53), timeout=5)
        return True
    except OSError:
        return False

def send_file(api_url, zip_path, user, today_date, device_name):
    """Attempts to send the zip file to the given API URL."""
    with open(zip_path, "rb") as zip_file:
        try:
            upload_endpoint = f"{api_url}/upload"
            response = requests.post(upload_endpoint, files={"file": zip_file}, data={"user": user, "today_date": today_date, "device_name": device_name}, timeout=180)
            return response
        except requests.exceptions.RequestException as e:
            print(f"Failed to reach {upload_endpoint}: {e}")
            CLogger.MSwriteLog(f"Failed to reach {upload_endpoint}: {e}",log_userwise=True,userName=user)
            return None
        
def check_file_on_server(file_path, user):
    """Checks if the file exists on the server by querying the API."""
    try:
        filename = os.path.basename(file_path)
        
        # Try each API endpoint
        for api_url in [API_LOCAL_URL, API_URL, API_URL2]:
            try:
                check_endpoint = f"{api_url}/check_file"
                response = requests.post(
                    check_endpoint, 
                    json={"filename": filename, "user": user},
                    timeout=180
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("exists", False)
            except requests.exceptions.RequestException:
                continue
                
        return False
    except Exception:
        return False  
          
def remove_old_logs(userFolderPath, days_threshold=10):
    """Removes log.csv files older than the specified threshold if they exist on server."""
    try:
        current_time = datetime.datetime.now()
        threshold_date = current_time - timedelta(days=days_threshold)
        
        # Check if server is available
        server_available = is_internet_available()
        if not server_available:
            CLogger.MSwriteLog("Server not available, skipping old log removal", "info")
            return
            
        # Get user info for logging
        user = helper.MSGetLoggedUserName().strip().lower()
        
        for root, _, files in os.walk(userFolderPath):
            for file in files:
                if file.endswith("Log.csv") or file.endswith("log.csv"):
                    file_path = os.path.join(root, file)
                    file_mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    # Check if file is older than threshold
                    if file_mod_time < threshold_date:
                        # Check if file exists on server before deleting
                        file_exists_on_server = check_file_on_server(file_path, user)
                        
                        if file_exists_on_server:
                            os.remove(file_path)
                            CLogger.MSwriteLog(f"Removed old log file: {file_path}", "info", log_userwise=True, userName=user)
    except Exception as e:
        user = helper.MSGetLoggedUserName().strip().lower()
        CLogger.MSwriteLog(f"Error removing old logs: {str(e)}", "error", log_userwise=True, userName=user)
        
def create_zip_and_send():
    config_path = "files\\Popup_Gui_Config.json"
    if not os.path.exists(config_path):
        CLogger.MSwriteLog("Config file not found.", level="error")
        return

    dict_config = helper.MSReadJSON(config_path)

    # Get UserData filepath
    if "UserData_filepath" not in dict_config or not os.path.exists(dict_config["UserData_filepath"]):
        CLogger.MSwriteLog("UserData_filepath not found in config or file does not exist.")
        print("UserData_filepath not found.")
        return
        
    dict_UserData = helper.MSReadJSON(dict_config["UserData_filepath"])

    # Get the user folder path
    user = helper.MSGetLoggedUserName().strip().lower()
    print(user)  # Debugging step
    CLogger.MSwriteLog(f"UserName {user}:","info",log_userwise=True,userName=user)

    if user not in dict_UserData:
        CLogger.MSwriteLog(f"User {user} not found in UserData. Using default user.",log_userwise=True,userName=user)
        user = "defaultuser"

    # Extract DataStore_path from the user's section
    if "DataStore_path" not in dict_UserData[user]:
        CLogger.MSwriteLog(f"DataStore_path not found for user: {user}",log_userwise=True,userName=user)
        print(f"DataStore_path not found for user: {user}")
        return    

    data_store_path = dict_UserData[user]["DataStore_path"]
    # Check if bEnableTransfer is True
    if not dict_UserData[user].get("bEnableTransfer", False):
        CLogger.MSwriteLog(f"Data transfer is disabled for user: {user}",log_userwise=True,userName=user)
        print(f"Data transfer is not enabled for user: {user}")
        return
        
    now = datetime.datetime.now()
    today_date = f"{now.year}_{now.month}_{now.day}"
    device_name = helper.MSGetComputerName()  # Get device name

    # Construct the correct paths
    userFolderPath = os.path.join(data_store_path, user) #, today_date, device_name
    # Load configurations

    # Prepare zip file name with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    zip_filename = f"data_{timestamp}.zip"
    zip_path = os.path.join(userFolderPath, zip_filename)

    # Create a zip file
    with zipfile.ZipFile(zip_path, "w") as zipf:
        for root, _, files in os.walk(userFolderPath):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith(".csv") or file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff')):
                    zipf.write(file_path, os.path.relpath(file_path, userFolderPath))
    # Send the zip file to the API
    try:
        response = send_file(API_LOCAL_URL, zip_path, user, today_date, device_name)
        
        if response is None or response.status_code != 200:
            print("Local API failed, trying static API...")
            CLogger.MSwriteLog("Local API failed, trying static API...","info",log_userwise=True,userName=user)
            response = send_file(API_URL, zip_path, user, today_date, device_name)
            
            if response is None or response.status_code != 200:
                print("Static API failed, trying backup API...")
                CLogger.MSwriteLog("Static API 1 failed, trying backup API...", "info", log_userwise=True, userName=user)
                response = send_file(API_URL2, zip_path, user, today_date, device_name)
            
        if response.status_code == 200:
            print("Upload successful, deleting image files...")
            CLogger.MSwriteLog("Upload successful, deleting image files...","info",log_userwise=True,userName=user)
            with open(SENT_IMAGES_FILE, "a") as sent_log:
                sent_log.write(f"{timestamp}: {zip_filename} uploaded successfully\n")
            
            # Delete image files but keep log.csv
            for root, _, files in os.walk(userFolderPath):
                for file in files:
                    file_path = os.path.join(root, file)
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff')):
                        os.remove(file_path)
        else:
            print("Upload failed, keeping all files intact...")
            CLogger.MSwriteLog("Upload failed, keeping all files intact...","info",log_userwise=True,userName=user)
            with open(FAILED_UPLOADS_FILE, "a") as failed_log:
                failed_log.write(f"{timestamp}: Failed to upload {zip_filename}\n")
            os.remove(zip_path)
    finally:
    # This will run no matter what
        if os.path.exists(zip_path):
            try:
                os.remove(zip_path)
                print(f"Deleted zip file: {zip_path}")
                CLogger.MSwriteLog(f"Deleted zip file: {zip_path}","info",log_userwise=True,userName=user)
            except Exception as del_err:
                CLogger.MSwriteLog(f"Error deleting zip file: {del_err}","info",log_userwise=True,userName=user)

def activate_admin_mode(self):
    try:
        # Make sure these URLs match the server's IP and port
        
        # Get URLs from config
        api_url = f"{config['API_URL']}/user_data"
        api_url_local = f"{config['API_URL_LOCAL']}/user_data"
        api_url2 = f"{config['API_URL2']}/user_data"
        
        customer_api_url = f"{config['API_URL']}/customer_data"
        customer_api_local = f"{config['API_URL_LOCAL']}/customer_data"
        customer_api_url2 = f"{config['API_URL2']}/customer_data"
        
        local_dir = os.path.join(os.path.dirname(__file__), "files")
        os.makedirs(local_dir, exist_ok=True)
        local_user_path = os.path.join(local_dir, "UserData.json")
        local_customer_path = os.path.join(local_dir, "CustomerData.json")

       # Try to download UserData.json from different URLs
        user_data_downloaded = False
        for url in [api_url_local, api_url, api_url2]:
            try:
                CLogger.MSwriteLog(f"Trying to download UserData.json from {url}", "info", log_userwise=True, userName=self.user)
                resp_user = requests.get(url, timeout=30)
                if resp_user.status_code == 200:
                    with open(local_user_path, "wb") as f:
                        f.write(resp_user.content)
                    user_data_downloaded = True
                    CLogger.MSwriteLog(f"Successfully downloaded UserData.json from {url}", "info", log_userwise=True, userName=self.user)
                    break
                else:
                    CLogger.MSwriteLog(f"Failed to download UserData.json from {url}: {resp_user.status_code}", "warning", log_userwise=True, userName=self.user)
            except Exception as e:
                CLogger.MSwriteLog(f"Error downloading UserData.json from {url}: {str(e)}", "warning", log_userwise=True, userName=self.user)

        if not user_data_downloaded:
            raise Exception("Failed to download UserData.json from all URLs")

        # Try to download CustomerData.json from different URLs
        customer_data_downloaded = False
        for url in [customer_api_local, customer_api_url, customer_api_url2]:
            try:
                CLogger.MSwriteLog(f"Trying to download CustomerData.json from {url}", "info", log_userwise=True, userName=self.user)
                resp_customer = requests.get(url, timeout=30)
                if resp_customer.status_code == 200:
                    with open(local_customer_path, "wb") as f:
                        f.write(resp_customer.content)
                    customer_data_downloaded = True
                    CLogger.MSwriteLog(f"Successfully downloaded CustomerData.json from {url}", "info", log_userwise=True, userName=self.user)
                    break
                else:
                    CLogger.MSwriteLog(f"Failed to download CustomerData.json from {url}: {resp_customer.status_code}", "warning", log_userwise=True, userName=self.user)
            except Exception as e:
                CLogger.MSwriteLog(f"Error downloading CustomerData.json from {url}: {str(e)}", "warning", log_userwise=True, userName=self.user)

        if not customer_data_downloaded:
            raise Exception("Failed to download CustomerData.json from all URLs")

        # Reload configs
        self.dictUsersData = helper.MSReadJSON(local_user_path)
        self.dictCustomerData = helper.MSReadJSON(local_customer_path)

        QMessageBox.information(None, "Admin Mode", "UserData & CustomerData downloaded and reloaded successfully.")
        CLogger.MSwriteLog("Admin Mode: JSON files downloaded via API and reloaded.", "info", log_userwise=True, userName=self.user)

    except Exception as e:
        QMessageBox.critical(None, "Admin Mode", f"Error: {str(e)}")
        CLogger.MSwriteLog(f"Admin Mode: Download error: {str(e)}", "error", log_userwise=True, userName=self.user)


if __name__ == "__main__":
    create_zip_and_send()
