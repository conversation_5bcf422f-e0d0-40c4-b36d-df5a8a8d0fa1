import ctypes
import time

class HIDFallbackActivityTracker:
    def __init__(self):
        self.last_check_time = time.time()

    def get_idle_duration(self) -> float:
        """Returns system idle time in seconds using Windows API"""
        class LASTINPUTINFO(ctypes.Structure):
            _fields_ = [("cbSize", ctypes.c_uint),
                        ("dwTime", ctypes.c_uint)]

        lii = LASTINPUTINFO()
        lii.cbSize = ctypes.sizeof(LASTINPUTINFO)
        if ctypes.windll.user32.GetLastInputInfo(ctypes.byref(lii)):
            millis = ctypes.windll.kernel32.GetTickCount() - lii.dwTime
            return millis / 1000.0
        else:
            return float('inf')

    def is_user_active(self, threshold_seconds=10) -> bool:
        """Returns True if the user was active within threshold_seconds"""
        return self.get_idle_duration() < threshold_seconds
