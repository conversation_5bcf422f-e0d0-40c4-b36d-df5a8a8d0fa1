from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form, HTTPException, Request
from fastapi.responses import JSONResponse,FileResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel
import shutil, os, traceback, json, zipfile
# from validateUser import validateUser, hash<PERSON><PERSON><PERSON><PERSON><PERSON>
from monHelper import CMonHelper as helper, <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from zoneinfo import ZoneInfo   
# Load configuration
with open("files\\Configurations.json", "r") as config_file:
    config = json.load(config_file)
    
IST = ZoneInfo("Asia/Kolkata")
DESTINATION_PATH = config["DESTINATION_PATH"]
REMOTE_PATH = config["USERDATA_REMOTE_PATH"]
CUSTOMERDATA_REMOTE_PATH = config["CUSTOMERDATA_REMOTE_PATH"]
# LOCAL_DIR = os.path.join(os.path.dirname(__file__), "files")
# LOCAL_PATH = os.path.join(LOCAL_DIR, "UserData.json")
# CUSTOMERDATA_LOCAL_PATH = os.path.join(LOCAL_DIR, "CustomerData.json")

app = FastAPI()

# Response model
class UploadResponse(BaseModel):
    status_code: int
    level: str
    msg: str
     
@app.get("/")
def read_root():
    return {"Health Check": "Excellent"}

@app.post("/upload", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    user: str = Form(...),
    today_date: str = Form(...),
    device_name: str = Form(...)
):
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file uploaded.")
        
        CLogger.MSwriteLog(f"Received request from user {user} with file {file.filename}", level="info")

        user_folder = os.path.join(DESTINATION_PATH, user)
        os.makedirs(user_folder, exist_ok=True)

        if not file.filename.lower().endswith('.zip'):
            raise HTTPException(status_code=400, detail="Only ZIP files are accepted.")

        # save incoming ZIP
        zip_path = os.path.join(user_folder, file.filename)
        with open(zip_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        if not zipfile.is_zipfile(zip_path):
            os.remove(zip_path)
            raise HTTPException(status_code=400, detail=f"Invalid ZIP file: {file.filename}")

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for zip_info in zip_ref.infolist():
                    extracted_path = os.path.join(user_folder, zip_info.filename)
                    if zip_info.is_dir():
                        continue

                    os.makedirs(os.path.dirname(extracted_path), exist_ok=True)

                    # --- NEW: size‑based logic for log.csv only ---
                    if zip_info.filename.lower().endswith("log.csv"):
                        new_size = zip_info.file_size
                        old_size = os.path.getsize(extracted_path) if os.path.exists(extracted_path) else 0

                        if new_size != old_size:
                            zip_ref.extract(zip_info, user_folder)
                            CLogger.MSwriteLog(
                                f"Replaced log.csv ({new_size} bytes vs {old_size} bytes) {zip_info.filename}", level="info"
                            )
                        else:
                            CLogger.MSwriteLog(
                                f"Skipped log.csv; size unchanged ({old_size} bytes) {zip_info.filename}", level="info"
                            )
                        continue
                    # --- end log.csv special case ---

                    # everything else: your existing timestamp logic
                    naive_dt         = datetime(*zip_info.date_time)
                    aware_dt         = naive_dt.replace(tzinfo=IST)
                    zip_mod_ts       = aware_dt.timestamp()
                    existing_mod_ts  = os.path.getmtime(extracted_path) if os.path.exists(extracted_path) else 0

                    if os.path.exists(extracted_path):
                        if zip_mod_ts > existing_mod_ts:
                            zip_ref.extract(zip_info, user_folder)
                            CLogger.MSwriteLog(f"Updated file {zip_info.filename}", level="info")
                        else:
                            CLogger.MSwriteLog(f"Skipped older file {zip_info.filename}", level="info")
                    else:
                        zip_ref.extract(zip_info, user_folder)
                        CLogger.MSwriteLog(f"Extracted new file {zip_info.filename}", level="info")

            os.remove(zip_path)
            msg = f"ZIP {file.filename} extracted to {user_folder}"
            CLogger.MSwriteLog(msg, level="info")
            return UploadResponse(status_code=200, level="info", msg=msg)

        except Exception as zip_err:
            raise HTTPException(status_code=500, detail=f"Error extracting ZIP: {zip_err}")

    except HTTPException as http_ex:
        CLogger.MSwriteLog(f"HTTPException: {http_ex.detail}", level="error")
        return UploadResponse(status_code=http_ex.status_code, level="error", msg=http_ex.detail)

    except Exception:
        error_trace = traceback.format_exc()
        CLogger.MSwriteLog(f"Exception: {error_trace}", level="error")
        raise HTTPException(status_code=500, detail="An internal server error occurred.")
    
app.post("/check_file")
async def check_file(request: Request):
    """
    Checks if a specific file exists for a user.
    Expects a JSON body with 'filename' and 'user' fields.
    """
    try:
        # Parse the request body
        data = await request.json()
        filename = data.get("filename")
        user = data.get("user")
        
        if not filename or not user:
            return JSONResponse(
                status_code=400,
                content={"exists": False, "message": "Missing filename or user"}
            )
        
        # Construct the path to the user's folder
        user_folder = os.path.join(DESTINATION_PATH, user)
        
        # Check if the file exists in the user's folder or subfolders
        file_exists = False
        for root, _, files in os.walk(user_folder):
            for file in files:
                if file == os.path.basename(filename):
                    file_exists = True
                    break
            if file_exists:
                break
        
        return JSONResponse(
            status_code=200,
            content={"exists": file_exists}
        )
    
    except Exception as e:
        print(f"Error checking file existence: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"exists": False, "message": "An error occurred while checking file"}
        )
        
@app.get("/user_data")
def download_user_data():
    return FileResponse(REMOTE_PATH, media_type="application/json", filename="UserData.json")

@app.get("/customer_data")
def download_customer_data():
    return FileResponse(CUSTOMERDATA_REMOTE_PATH, media_type="application/json", filename="CustomerData.json")


#uvicorn DataReceiving:app --host 0.0.0.0 --port 5010 --workers 2        --> Run on porn 5010 PRoduction use
#uvicorn DataReceiving:app --host 0.0.0.0 --port 9090 --workers 2        --> Run on porn 9090 for development on RiverDev11
# uvicorn DataReceiving:app --host ************ --port 8008 --workers 2

